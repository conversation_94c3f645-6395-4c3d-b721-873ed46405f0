package doJDStackFilecenter

import (
	"encoding/json"
	"reflect"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

func init() {
	g.View().BindFuncMap(g.Map{
		"deployMode2FileType": DeployMode2FileType,
		"downloadAddress":     CommandDownloadAddress.Parse,
		"caseKebab":           gstr.CaseKebab,
		"jdock2FileType":      Jdock2FileType,
	})
}

// DeployMode2FileType 将部署类型转成文件类型
// 待废弃：5.4.0 希望改成不按照部署类型划分目录
func DeployMode2FileType(deployMode string) string {
	switch deployMode {
	case DeployModeSkywingContainer:
		return FileTypeDockerImage
	case DeployModeSkywingPackage:
		return FileTypeSkywingPackage
	case DeployModeMateContainer:
		return FileTypeDockerImage
	case DeployModeShell:
		return FileTypeRegularFile
	case DeployModeHelm:
		return FileTypeDockerImage
	case DeployModeRpm:
		return FileTypeRpm
	case DeployModeMateBin:
		return FileTypeRpm
	case DeployModeMateV2:
		return FileTypeRpm
	case DeployModeIaasImage:
		return FileTypeIaasImage
	case DeployModeXingPackage:
		return FileTypeXingyunPackage
	case DeployModeXingContainer:
		return FileTypeDockerImage
	}
	return ""
}
func Jdock2FileType(jdockFileType string) string {
	switch jdockFileType {
	case JdockFileTypeImage:
		return FileTypeDockerImage
	case JdockFileTypeOutImage:
		return FileTypeDockerImage
	case JdockFileTypeOsFile:
		return FileTypeRegularFile
	case JdockFileTypeRpm:
		return FileTypeRpm
	case JdockFileTypeHelm:
		return FileTypeHelm
	case JdockFileTypePackage:
		return FileTypePackage
	}
	return ""
}
func Jdock2Arch(jdockArch string) string {
	switch jdockArch {
	case JdockAmd:
		return ArchX86
	case JdockArm:
		return ArchArm
	case ArchX86:
		return ArchX86
	case ArchArm:
		return ArchArm
	}
	return ""
}
func Jdock2Os(jdockOs string) string {
	switch jdockOs {
	case JdockCentos:
		return OsCentos
	case JdockOpeneuler:
		return osOpeneuler
	case JdockKylin:
		return OsKylin
	}
	return ""
}

func Jdock2Oses(jdockOses []string) []string {
	var res []string
	for _, v := range jdockOses {
		res = append(res, Jdock2Os(v))
	}
	return res
}

// 判断文本是否为URL
func isFileURL(url string) bool {
	return regexFileURL.MatchString(url)
}

// 判断文本是否为Harbor
func IsHarborURL(url string) bool {
	return regexHarborURL.MatchString(url)
}

// 判断是否是天基触发的构建
func IsJdockImage(image string) bool {
	return strings.HasPrefix(image, "hub.jdcloud.com") || strings.HasPrefix(image, "hub-vpc.jdcloud.com")
}

// 解析远程地址字符串并返回
func parseRemoteAddress(address string) *ModelRemoteAddressReply {
	res := &ModelRemoteAddressReply{
		Username: "root", // 默认用户名
		Port:     "22",   // 默认端口
	}

	// 分割用户名和主机名部分
	atIndex := strings.Index(address, "@")
	if atIndex != -1 {
		res.Username = address[:atIndex]
		address = address[atIndex+1:]
	}

	// 分割路径部分
	pathIndex := strings.Index(address, ":/")
	if pathIndex != -1 {
		res.Path = address[pathIndex+1:]
		address = address[:pathIndex]
	}

	// 分割端口部分
	colonIndex := strings.LastIndex(address, ":")
	if colonIndex != -1 {
		res.Port = address[colonIndex+1:]
		address = address[:colonIndex]
	}

	// 剩下的部分就是主机名或IP地址
	res.Host = address

	return res
}

// func IndexOf(s []string, v string) int {
// 	for i, x := range s {
// 		if x == v {
// 			return i
// 		}
// 	}
// 	return -1
// }

func Address2CommitID(address string) (commitId string) {
	tag := Address2ImageTag(address)
	if tag == "" {
		return
	}

	match := regexImageCommit.FindStringSubmatch(tag)
	if len(match) > 1 {
		for i := 1; i < cap(match); i++ {
			if match[i] != "" {
				commitId = match[i]
				break
			}
		}
	}
	return
}

// Address2Filename 从地址提取文件名
func Address2Filename(address string) (filename string) {
	if IsHarborURL(address) {
		// NOTE: 如果是天基触发的构建,则将镜像保存成{image_name}_{tag}.tar
		if IsJdockImage(address) {
			filename = gstr.SubStrFromREx(address, "/")
			filename = strings.ReplaceAll(filename, ":", "_") + ".tar"
		} else {
			// JDStack老方式
			// 最后一个冒号后面加.tar
			filename = gstr.SubStrFromREx(address, ":") + ".tar"
		}
	} else if isFileURL(address) {
		// url
		match := regexFileURL.FindStringSubmatch(address)
		if len(match) > 2 {
			filename = gstr.SubStrFromREx(match[len(match)-2], "/")
		}
	} else {
		// remote
		filename = gstr.SubStrFromREx(address, "/")
	}
	return
}

// Address2ImageTag 从地址提取镜像Tag
func Address2ImageTag(address string) (tag string) {
	tag = address
	// 移除地址中的参数
	if i := strings.Index(tag, "?"); i > -1 {
		tag = tag[:i]
	}
	// 移除地址中的目录
	if i := strings.LastIndex(tag, "/"); i > -1 {
		tag = tag[i+1:]
	}
	// 移除扩展名
	match := regexFileExt.FindStringSubmatch(tag)
	if len(match) > 1 {
		tag = match[1]
	}
	// 直接返回harbor冒号的后半部
	if i := strings.LastIndex(tag, ":"); i > -1 {
		return tag[i+1:]
	}

	if len(tag) < 30 { // 长度低于条件值就不分割
		return
	}

	match = regexImageTag.FindStringSubmatch(tag)
	matchTag := ""
	if len(match) > 1 {
		matchTag = match[1]
	}

	minusIndex := strings.LastIndex(tag, "-") // Mate是通过minus分割产品名称和版本号的，这里与正则比谁匹配更多就用谁
	if matchTag != "" && (minusIndex == -1 || strings.Index(tag, matchTag) <= minusIndex+1) {
		tag = matchTag
	} else if len(tag)-minusIndex > 10 { //  长度低于条件值就不分割
		tag = tag[minusIndex+1:]
	}
	return tag
}

// ToPartialJSON converts specified fields of a struct to JSON.
func ToPartialJSON(input interface{}, fields []string) string {
	// Create a map to hold the selected fields and their values.
	selectedFields := make(map[string]interface{})

	// Use reflection to iterate over the struct fields.
	v := reflect.ValueOf(input)

	// Ensure the input is a struct.
	if v.Kind() != reflect.Struct {
		return ""
	}

	// Iterate over the requested fields.
	for _, fieldName := range fields {
		// Get the field by name.
		fieldVal := v.FieldByName(fieldName)
		if !fieldVal.IsValid() {
			continue
		}

		// Add the field to the map.
		selectedFields[fieldName] = fieldVal.Interface()
	}

	// Convert the map to JSON.
	jsonData, err := json.Marshal(selectedFields)
	if err != nil {
		return ""
	}

	return string(jsonData)
}

// GetDownloadStage 根据文件类型返回下载阶段
// os_file类型只需要doing和postcheck阶段，因为普通文件要往文件名后缀加md5，每次都是重新拉文件
func GetDownloadStage(fileType string) []string {
	if fileType == FileTypeRegularFile {
		return []string{"doing", "postcheck"}
	}
	return []string{"precheck", "doing", "postcheck"}
}

func ToPartialJSONWithMap(input interface{}, fields []string, fieldMap map[string]string) map[string]interface{} {
	// Create a map to hold the selected fields and their values.
	selectedFields := make(map[string]interface{})

	// Use reflection to iterate over the struct fields.
	v := reflect.ValueOf(input)

	// Ensure the input is a struct.
	if v.Kind() != reflect.Struct {
		return nil
	}

	// Iterate over the requested fields.
	for _, fieldName := range fields {
		// Get the real name from the fieldMap.
		realName, ok := fieldMap[fieldName]
		if !ok {
			realName = fieldName
		}

		// Get the field by name.
		fieldVal := v.FieldByName(fieldName)
		if !fieldVal.IsValid() || fieldVal.IsZero() {
			continue
		}

		// Add the field to the map.
		selectedFields[realName] = fieldVal.Interface()
	}

	return selectedFields
}
