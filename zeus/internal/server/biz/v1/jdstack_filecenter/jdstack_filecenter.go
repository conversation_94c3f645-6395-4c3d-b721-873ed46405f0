package bizJDStackFilecenter

import (
	doVersion "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/version"
	"context"
	"errors"

	"github.com/appleboy/easyssh-proxy"
	"github.com/go-kratos/kratos/v2/log"

	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/conf"
	doGoodsStack "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/goods_stack"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
	dMutex "coding.jd.com/pcd-application/win-go/distributed_mutex"
	jDto "coding.jd.com/pcd-application/win-go/dto"
	jErr "coding.jd.com/pcd-application/win-go/error"
	moTaskType "coding.jd.com/pcd-application/win-go/project/data/task/model/task_type"
	doTask "coding.jd.com/pcd-application/win-go/project/domain/task"
)

const Self = conf.DomainCode_JDStackFilecenter

type jdstackFilecenterUsecase struct {
	log                  *log.Helper
	DTO                  jDto.IDTO
	lock                 dMutex.IDMutex
	cliSsh               *easyssh.MakeConfig
	FilecenterConf       *conf.Other_Filecenter
	filecenterUploadTask []func(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterUploadTaskParams) (isRemove bool, err error)

	GoodsStackCase      doGoodsStack.GoodsStackCase
	JdstackReleaseRepo  doVersion.JdstackReleaseRepo
	GoodsRepo           doGoodsStack.GoodsRepo
	GoodsFilecenterRepo doGoodsStack.GoodsFilecenterRepo
	TaskCase            doTask.TaskCase
	TaskRepo            doTask.TaskRepo
}

func (uc *jdstackFilecenterUsecase) CreateArtifactPackage(ctx context.Context, req *pbJdstackFilecenter.CreateArtifactPackageReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) CreateDockerImageToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.DockerImageTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) CreateMetadataPackage(ctx context.Context, req *pbJdstackFilecenter.CreateMetadataPackageReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) CreateRegularFileToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.RegularFileTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) CreateRpmToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.RpmTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) CreateSkywingPackageToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.SkywingPackageTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) DescribeArtifactPackage(ctx context.Context, req *pbJdstackFilecenter.DescribeArtifactPackageReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) DescribeDockerImageToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.DockerImageTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) DescribeMetadataPackage(ctx context.Context, req *pbJdstackFilecenter.DescribeMetadataPackageReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) DescribeRegularFileToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.RegularFileTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) DescribeRpmToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.RpmTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func (uc *jdstackFilecenterUsecase) DescribeSkywingPackageToEnvRepoTask(ctx context.Context, req *pbJdstackFilecenter.DockerImageTaskReq) jErr.Error {
	// TODO implement me
	panic("implement me")
}

func NewJDStackFilecenter(
	logger log.Logger,
	DTO jDto.IDTO,
	lock dMutex.IDMutex,
	otherConf *conf.Other,

	GoodsStackCase doGoodsStack.GoodsStackCase,
	GoodsRepo doGoodsStack.GoodsRepo,
	GoodsFilecenterRepo doGoodsStack.GoodsFilecenterRepo,
	TaskCase doTask.TaskCase,
	TaskRepo doTask.TaskRepo,
) (doJDStackFilecenter.JDStackFilecenterCase, error) {
	if otherConf.Filecenter == nil || otherConf.Filecenter.Ssh == nil {
		return nil, errors.New("invalid Bootstrap.Other.Filecenter.Ssh: value is required")
	}

	uc := &jdstackFilecenterUsecase{
		log:  log.NewHelper(log.With(logger, "module", "biz/jdstackFilecenter")),
		DTO:  DTO,
		lock: lock,
		cliSsh: &easyssh.MakeConfig{
			Server:  otherConf.Filecenter.Ssh.Server,
			Port:    otherConf.Filecenter.Ssh.Port,
			User:    otherConf.Filecenter.Ssh.User,
			KeyPath: otherConf.Filecenter.Ssh.KeyPath,
		},
		FilecenterConf:      otherConf.Filecenter,
		GoodsStackCase:      GoodsStackCase,
		GoodsRepo:           GoodsRepo,
		GoodsFilecenterRepo: GoodsFilecenterRepo,
		TaskCase:            TaskCase,
		TaskRepo:            TaskRepo,
	}

	if otherConf.Filecenter.Ssh.Proxy != nil {
		uc.cliSsh.Proxy = easyssh.DefaultConfig{
			Server:  otherConf.Filecenter.Ssh.Proxy.Server,
			Port:    otherConf.Filecenter.Ssh.Proxy.Port,
			User:    otherConf.Filecenter.Ssh.Proxy.User,
			KeyPath: otherConf.Filecenter.Ssh.Proxy.KeyPath,
		}
	}

	if err := TaskCase.RegisterHandler(moTaskType.New("FilecenterUpload"), uc.filecenterUploadTaskHandler); err != nil {
		return nil, err
	}
	if err := TaskCase.RegisterHandler(moTaskType.New("FilecenterDownload"), uc.filecenterDownloadTaskHandler); err != nil {
		return nil, err
	}
	if err := TaskCase.RegisterHandler(moTaskType.New("FilecenterAppFilesPush"), uc.filecenterAppFilesPushTaskHandler); err != nil {
		return nil, err
	}
	if err := TaskCase.RegisterHandler(moTaskType.New("FilecenterArtifactPackage"), uc.filecenterArtifactPackageTaskHandler); err != nil {
		return nil, err
	}
	if err := TaskCase.RegisterHandler(moTaskType.New("FilecenterSCFilesPush"), uc.filecenterSCFilesPushTaskHandler); err != nil {
		return nil, err
	}

	return uc, nil
}
