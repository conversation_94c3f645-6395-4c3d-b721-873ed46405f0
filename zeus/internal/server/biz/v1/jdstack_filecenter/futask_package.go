package bizJDStackFilecenter

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/jdstackrelease"

	"github.com/gogf/gf/v2/util/gconv"

	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goods"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodsfilecenter"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadataservice"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
	jConvert "coding.jd.com/pcd-application/win-go/convert"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"
)

// ErrorDetail 存储单个错误的详细信息
type ErrorDetail struct {
	FilePath string `json:"file_path,omitempty"`
	Message  string `json:"message,omitempty"`
	FileID   int64  `json:"file_id,omitempty"`
}

// ErrorCollection 用于收集和结构化存储错误信息
type ErrorCollection struct {
	Errors []ErrorDetail `json:"errors"`
	Count  int           `json:"count"`
}

func (ec *ErrorCollection) AddError(fileID int64, filePath, message string) {
	ec.Errors = append(ec.Errors, ErrorDetail{
		FilePath: filePath,
		Message:  message,
		FileID:   fileID,
	})
	ec.Count++
}

func (ec *ErrorCollection) HasErrors() bool {
	return ec.Count > 0
}

// NeedExit 认定错误数量大于20的时候需要强制退出
func (ec *ErrorCollection) NeedExit() bool {
	return ec.Count > 20
}

func (ec *ErrorCollection) ToJSON() string {
	bytes, err := json.Marshal(ec)
	if err != nil {
		return fmt.Sprintf("{\"errors\":[{\"message\":\"错误序列化失败: %s\"}],\"count\":1}", err.Error())
	}
	return string(bytes)
}

func (uc *jdstackFilecenterUsecase) filecenterTaskPackage(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams) (isRemove bool, err error) {
	// 初始化错误收集器
	errorCollection := &ErrorCollection{
		Errors: make([]ErrorDetail, 0),
		Count:  0,
	}

	// 检查当前版本最新修改时间是否晚于快照时间，需要在req中添加快照生成时间
	filters := winapi.Filters{
		&winapi.Filter{
			Name:   jdstackrelease.FieldName,
			Values: []string{data.Params.PackageVersion},
		},
		&winapi.Filter{
			Name:   jdstackrelease.FieldCloudType,
			Values: []string{data.Params.PackageVersionType},
		},
	}
	jdstackReleaseDao, jError := uc.JdstackReleaseRepo.GetName(ctx, data.Params.PackageVersion, filters...)
	if jError != nil {
		reason := fmt.Sprintf("查询打包封版版本失败, packageVersion: %s, packageVersionType: %s", data.Params.PackageVersion, data.Params.PackageVersionType)
		jError = jError.AddReasonf(reason)
		return true, jError
	}
	snapshotGeneratedTime := time.Unix(data.Params.SnapshotGeneratedTimeStamp, 0)
	if jdstackReleaseDao.UpdatedAt.After(snapshotGeneratedTime) {
		reason := fmt.Sprintf("当前版本中心zeus.sql最新修改时间(%s)晚于快照生成时间(%s), 认定版本数据存在变更，需要重新在天基封版版本内生成快照，并重新在当前部署方案关联，重新触发制品包生成", jdstackReleaseDao.UpdatedAt.String(), snapshotGeneratedTime.String())
		errorCollection.AddError(0, "", reason)
		data.Log = errorCollection.ToJSON()
		data.IsFailed = true
		return true, fmt.Errorf(reason)
	}

	// 定义用于并发执行的变量
	var wg sync.WaitGroup
	var yumSyncErr error
	var yumSyncMutex sync.Mutex
	var yumSyncStarted bool // 标记是否启动了yum同步goroutine

	// 检查出包前是否要清空相应制品目录，目前仅清空当前架构目录和公共yum目录
	// 为避免误删除正在同步的yum目录，需要联动handleJdstackYumSync函数做检查
	if data.Params.ClearArtifactDirBeforePackage {
		if err := uc.clearArtifactDirectories(ctx, data, errorCollection); err != nil {
			errorCollection.AddError(0, "", fmt.Sprintf("清空制品目录失败: %s", err.Error()))
			data.Log = errorCollection.ToJSON()
			data.IsFailed = true
			return true, fmt.Errorf("清空制品目录失败: %s", err.Error())
		}
	}

	// 处理 JdstackReleaseS3 下载逻辑
	// 等同于在3.14zeus所在机器ssh3.44出包机器向3.17执行命令
	if data.Params.JdstackReleaseS3 != "" {
		// 根据任务类型决定下载路径
		if err := uc.handleJdstackReleaseS3Download(ctx, data, errorCollection); err != nil {
			errorCollection.AddError(0, "", fmt.Sprintf("JdstackReleaseS3下载失败: %s", err.Error()))
			data.Log = errorCollection.ToJSON()
			data.IsFailed = true
			return true, fmt.Errorf("JdstackReleaseS3下载失败: %s", err.Error())
		}
	}

	// 处理 Yum 源同步逻辑，从 5.6.0 之后的版本引入，此公共 yum 源长期不变，每个版本触发打包，只需从目标目录同步一次即可，优化掉之前的 3.11 机器上的打包脚本方式
	// 将 yum 源目录从目标机器上的一个目录同步到另一个目录，jdstack_yum_path是在出包机上统一维护的 yum 目录，实际从 3.11 机器上的 yum 源目录而来
	// 使用 goroutine 并发执行 yum 同步，与后续打包过程并行
	// 升级包场景无需同步 yum 源
	if _, ok := uc.FilecenterConf.Cli["jdstack_yum_path"]; ok {
		if data.Params.TaskType == "full" {
			yumSyncStarted = true // 标记已启动yum同步
			dataTmp := *data
			wg.Add(1)
			go func() {
				defer wg.Done()
				err := uc.handleJdstackYumSync(ctx, &dataTmp)
				if err != nil {
					yumSyncMutex.Lock()
					yumSyncErr = err
					yumSyncMutex.Unlock()
				}
			}()
		}
	}

	packageFile := &doJDStackFilecenter.ModelVersionPackageDestReply{}

	// 直接获取要处理的 GoodsFilecenter 数据
	var filecenters []*dao.GoodsFilecenter

	if data.Params.TaskType == "increment" {
		// 升级包场景：需要比较两个版本的 goodsFilecenter 数据
		if data.Params.BaseVersion == "" {
			errorCollection.AddError(0, "", "升级包场景下必须提供基准版本(BaseVersion)")
			data.Log = errorCollection.ToJSON()
			data.IsFailed = true
			return true, fmt.Errorf("INTERNAL JDStackFilecenter:出包失败 JDStackFilecenter:%s", errorCollection.ToJSON())
		}

		// 直接获取增量包需要的文件列表
		filecenters, err = uc.getIncrementalFilecenters(ctx, data, errorCollection)
		if err != nil {
			data.Log = errorCollection.ToJSON()
			data.IsFailed = true
			return true, fmt.Errorf("INTERNAL JDStackFilecenter:出包失败 JDStackFilecenter:%s", errorCollection.ToJSON())
		}

		uc.log.Debugw("升级包场景：找到新增制品数量", "count", len(filecenters))
	} else {
		// 全量包场景：获取所有制品
		filecenters, err = uc.getFullPackageFilecenters(ctx, data, errorCollection)
		if err != nil {
			data.Log = errorCollection.ToJSON()
			data.IsFailed = true
			return true, fmt.Errorf("INTERNAL JDStackFilecenter:出包失败 JDStackFilecenter:%s", errorCollection.ToJSON())
		}
	}

	// 封装打包入参
	num := 0
	rsyncBwLimit := int64(300 * 1024) // rsync默认带宽设置
	if data.RsyncBwLimit != 0 {
		rsyncBwLimit = data.RsyncBwLimit * 1024
	}

	// 统一的出包处理循环
	for _, filecenter := range filecenters {
		// 测试限定
		if uc.FilecenterConf.Cli["is_test"] == "yes" && num > 10 {
			return true, nil
		}

		// 如果累计错误超过20个就需要强制退出了
		if errorCollection.NeedExit() {
			// 将错误信息结构化存储到Log字段中
			data.Log = errorCollection.ToJSON()
			// 设置任务失败状态
			data.IsFailed = true
			return true, fmt.Errorf("出包失败，已累计%d个错误，达到累计错误上限，强制结束出包任务", errorCollection.Count)
		}

		packageCommonPath := fmt.Sprintf("%s/", strings.TrimSuffix(uc.FilecenterConf.Cli["package_path"], "/"))
		fileRelativePath := strings.TrimPrefix(*filecenter.FilePath, packageCommonPath)

		// 禁止以目录结尾的脏数据进入出包过程
		if strings.HasSuffix(fileRelativePath, "/") {
			// 不立即返回错误，而是记录错误并继续
			errorMsg := fmt.Sprintf("filecenter %d FilePath(%s)以目录结尾,属于违规数据", filecenter.ID, *filecenter.FilePath)
			errorCollection.AddError(filecenter.ID, *filecenter.FilePath, errorMsg)
			continue
		}

		fileMd5 := ""
		if filecenter.FileHash != nil && *filecenter.FileHash != "" {
			fileMd5 = *filecenter.FileHash
		} else {
			// 不立即返回错误，而是记录错误并继续
			errorMsg := fmt.Sprintf("filecenter %d fileHash为空(online应用此类数据为异常)", filecenter.ID)
			errorCollection.AddError(filecenter.ID, *filecenter.FilePath, errorMsg)
			continue
		}

		newData := &doJDStackFilecenter.ModelVersionPackageDestReq{
			DestHost:        data.Params.DestHostIp,
			DestPort:        gconv.String(data.Params.DestSshPort),
			DestPackagePath: data.Params.DestPath,
			PackagePath:     packageCommonPath,
			Version:         data.Params.PackageVersion,
			PackageFilePath: fileRelativePath,
			Md5:             fileMd5,
			RsyncBwLimit:    jConvert.AnyNillable(rsyncBwLimit),
		}

		var pkgErr error
		if packageFile, pkgErr = uc.doVersionPackageDest(ctx, newData); pkgErr != nil {
			// 不立即返回错误，而是记录错误并继续
			errorCollection.AddError(filecenter.ID, *filecenter.FilePath, pkgErr.Error())
			continue
		}

		if !packageFile.Success {
			// 不立即返回错误，而是记录错误并继续
			errorCollection.AddError(filecenter.ID, *filecenter.FilePath, gconv.String(packageFile))
		}

		num++
	}

	// 只有在启动了yum同步时才等待其完成
	if yumSyncStarted {
		// 等待 yum 同步完成
		wg.Wait()

		// 检查 yum 同步是否成功
		if yumSyncErr != nil {
			errorCollection.AddError(0, "", fmt.Sprintf("yum源目录同步失败: %s", yumSyncErr.Error()))
		}
	}

	// 所有任务执行完毕后，检查是否有错误
	if errorCollection.HasErrors() {
		// 将错误信息结构化存储到Log字段中
		data.Log = errorCollection.ToJSON()
		// 设置任务失败状态
		data.IsFailed = true
		return true, fmt.Errorf("出包失败，共有%d个错误", errorCollection.Count)
	}

	return true, nil
}

// handleJdstackReleaseS3Download 处理 JdstackReleaseS3 下载逻辑
func (uc *jdstackFilecenterUsecase) handleJdstackReleaseS3Download(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams, errorCollection *ErrorCollection) error {
	var targetDir string

	// 根据任务类型决定下载路径
	if data.Params.TaskType == "increment" {
		// 升级包场景：jdstackRelease 要下载到 packageversion 子目录下的 meta_data 目录下
		targetDir = filepath.Join(
			data.Params.DestPath,
			data.Params.PackageVersion,
			"meta_data",
		)
	} else {
		// 全量包场景：原有逻辑
		targetDir = filepath.Join(
			data.Params.DestPath,
			data.Params.PackageVersion,
			doJDStackFilecenter.Jdock2Arch(data.Params.Architecture),
			"jdstack_release",
		)
	}

	// 目标文件路径
	targetFile := filepath.Join(targetDir, "jdstack-release.tar.gz")

	// 下载文件到目标主机
	if err := uc.downloadS3FileToDestHost(ctx, data, targetDir, targetFile); err != nil {
		return fmt.Errorf("下载S3文件失败: %s", err.Error())
	}

	// 在目标主机上创建 info 文件
	if err := uc.createInfoFileOnDestHost(ctx, data, targetDir); err != nil {
		return fmt.Errorf("创建info文件失败: %s", err.Error())
	}

	return nil
}

// downloadS3FileToDestHost 下载S3文件到目标主机
func (uc *jdstackFilecenterUsecase) downloadS3FileToDestHost(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams, targetDir, targetFile string) error {
	// 构建SSH命令，参照 version_package_dest.tmpl 的模式
	// 1. 清空并创建目录
	// 2. 下载文件

	destPort := gconv.String(data.Params.DestSshPort)
	destHost := data.Params.DestHostIp
	s3URL := data.Params.JdstackReleaseS3

	// 构建完整的命令，包括清空目录、创建目录、下载文件
	cmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'rm -rf %s && mkdir -p %s && (curl -L -o %s %s || wget -O %s %s) && test -f %s && echo \"download_success\" || echo \"download_failed\"'",
		destPort, destHost, targetDir, targetDir, targetFile, s3URL, targetFile, s3URL, targetFile,
	)

	uc.log.Debugw("开始在目标主机下载JdstackReleaseS3文件", "cmd", cmd, "s3URL", s3URL, "targetFile", targetFile)

	// 执行下载命令
	stdout, stderr, done, sshErr := uc.cliSsh.Run(cmd, 30*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("SSH执行失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("下载超时或失败, stderr: %s", stderr)
	}

	// 检查下载是否成功
	if !strings.Contains(stdout, "download_success") {
		return fmt.Errorf("文件下载失败，stdout: %s, stderr: %s", stdout, stderr)
	}

	uc.log.Debugw("JdstackReleaseS3下载完成", "stdout", stdout, "stderr", stderr)

	return nil
}

// createInfoFileOnDestHost 在目标主机上创建info文件
func (uc *jdstackFilecenterUsecase) createInfoFileOnDestHost(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams, targetDir string) error {
	destPort := gconv.String(data.Params.DestSshPort)
	destHost := data.Params.DestHostIp

	// 构建info文件内容
	infoContent := fmt.Sprintf("操作时间: %s\\nS3地址: %s\\n",
		time.Now().Format("2006-01-02 15:04:05"),
		data.Params.JdstackReleaseS3)

	infoFile := filepath.Join(targetDir, "info")

	// 构建创建info文件的SSH命令
	cmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'echo -e \"%s\" > %s && echo \"info_created\" || echo \"info_failed\"'",
		destPort, destHost, infoContent, infoFile,
	)

	uc.log.Debugw("开始在目标主机创建info文件", "cmd", cmd, "infoFile", infoFile)

	// 执行创建info文件命令
	stdout, stderr, done, sshErr := uc.cliSsh.Run(cmd, 1*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("SSH执行失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("创建info文件超时或失败, stderr: %s", stderr)
	}

	// 检查info文件是否创建成功
	if !strings.Contains(stdout, "info_created") {
		return fmt.Errorf("info文件创建失败，stdout: %s, stderr: %s", stdout, stderr)
	}

	uc.log.Debugw("info文件创建完成", "stdout", stdout, "stderr", stderr)

	return nil
}

// clearArtifactDirectories 清空制品目录，通过标记文件避免与yum同步冲突
// 策略：总是清理当前架构目录，检查yum同步标记文件决定是否清理yum目录，注意这里存在和handleJdstackYumSync标记文件的联动
func (uc *jdstackFilecenterUsecase) clearArtifactDirectories(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams, errorCollection *ErrorCollection) error {
	// 构建目标目录路径
	targetDir := filepath.Join(
		data.Params.DestPath,
		data.Params.PackageVersion,
	)

	destPort := gconv.String(data.Params.DestSshPort)
	destHost := data.Params.DestHostIp

	// 获取架构目录名称（将 amd64/arm64 转换为 x86_64/arm）
	archDir := doJDStackFilecenter.Jdock2Arch(data.Params.Architecture)
	archDirPath := filepath.Join(targetDir, archDir)
	yumDir := filepath.Join(targetDir, "yum")
	// 使用通用的yum同步标记文件，不区分架构，因为yum目录是所有架构共享的
	yumSyncMarker := filepath.Join(targetDir, ".yum_sync_in_progress")

	uc.log.Debugw("开始清空制品目录", "targetDir", targetDir, "archDir", archDir, "archDirPath", archDirPath)

	// 构建清理命令
	clearCmd := fmt.Sprintf(`
# 1. 总是清理当前架构目录（安全操作，不会影响其他架构）
rm -rf %s
echo "已清理架构目录: %s"

# 2. 检查yum同步标记文件，如果不存在才清理yum目录
if [ ! -f "%s" ]; then
	echo "yum同步标记文件不存在，清理yum目录"
	rm -rf %s
	echo "已清理yum目录: %s"
else
	echo "yum同步正在进行中，跳过yum目录清理"
fi
`, archDirPath, archDirPath, yumSyncMarker, yumDir, yumDir)

	// 构建完整的SSH命令
	cmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s '%s && echo \"clear_success\"'",
		destPort, destHost, clearCmd,
	)

	uc.log.Debugw("执行清空制品目录命令", "cmd", cmd)

	// 执行删除命令
	stdout, stderr, done, sshErr := uc.cliSsh.Run(cmd, 5*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("SSH执行失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("清空目录超时或失败, stderr: %s", stderr)
	}

	// 检查删除是否成功
	if !strings.Contains(stdout, "clear_success") {
		return fmt.Errorf("清空目录失败，stdout: %s, stderr: %s", stdout, stderr)
	}

	uc.log.Debugw("制品目录清空完成", "stdout", stdout, "stderr", stderr, "archDir", archDirPath, "yumDir", yumDir)

	return nil
}

// handleJdstackYumSync 处理 Yum 源同步逻辑
// 将 yum 源目录从 uc.FilecenterConf.Cli["jdstack_yum_path"] 同步到 data.Params.DestPath/data.Params.PackageVersion 目录下
// 只在目标目录下没有 yum 目录时执行，如果已经有则跳过
// 使用标记文件避免与clearArtifactDirectories冲突
func (uc *jdstackFilecenterUsecase) handleJdstackYumSync(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams) error {
	// 获取 yum 源路径
	yumSourcePath, ok := uc.FilecenterConf.Cli["jdstack_yum_path"]
	if !ok || yumSourcePath == "" {
		return fmt.Errorf("未配置 jdstack_yum_path")
	}

	// 处理源路径末尾可能有斜杠的情况
	yumSourcePath = strings.TrimSuffix(yumSourcePath, "/")

	// 构建目标路径（在目标主机上）
	targetDir := filepath.Join(
		data.Params.DestPath,
		data.Params.PackageVersion,
	)

	destPort := gconv.String(data.Params.DestSshPort)
	destHost := data.Params.DestHostIp
	yumSyncMarker := filepath.Join(targetDir, ".yum_sync_in_progress")

	// 创建yum同步标记文件，防止clearArtifactDirectories删除yum目录
	createMarkerCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'mkdir -p %s && touch %s && echo \"marker_created\"'",
		destPort, destHost, targetDir, yumSyncMarker,
	)

	uc.log.Debugw("创建yum同步标记文件", "cmd", createMarkerCmd, "marker", yumSyncMarker)

	stdout, stderr, done, sshErr := uc.cliSsh.Run(createMarkerCmd, 1*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("创建yum同步标记文件失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done || !strings.Contains(stdout, "marker_created") {
		return fmt.Errorf("创建yum同步标记文件失败, stdout: %s, stderr: %s", stdout, stderr)
	}

	// 确保在函数结束时删除标记文件
	defer func() {
		deleteMarkerCmd := fmt.Sprintf(
			"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'rm -f %s && echo \"marker_deleted\"'",
			destPort, destHost, yumSyncMarker,
		)

		uc.log.Debugw("删除yum同步标记文件", "cmd", deleteMarkerCmd, "marker", yumSyncMarker)

		stdout, stderr, done, sshErr := uc.cliSsh.Run(deleteMarkerCmd, 1*time.Minute)
		if sshErr != nil {
			uc.log.Errorw("删除yum同步标记文件失败", "error", sshErr.Error(), "stderr", stderr)
		} else if !done || !strings.Contains(stdout, "marker_deleted") {
			uc.log.Errorw("删除yum同步标记文件失败", "stdout", stdout, "stderr", stderr)
		} else {
			uc.log.Debugw("yum同步标记文件删除成功", "marker", yumSyncMarker)
		}
	}()

	// 首先检查目标目录是否已存在 yum 目录
	checkCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'if [ -d \"%s/yum\" ]; then echo \"yum_exists\"; else echo \"yum_not_exists\"; fi'",
		destPort, destHost, targetDir,
	)

	uc.log.Debugw("检查目标主机是否已存在yum目录", "cmd", checkCmd, "targetDir", targetDir)

	// 执行检查命令
	stdout, stderr, done, sshErr = uc.cliSsh.Run(checkCmd, 1*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("SSH执行失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("检查yum目录超时或失败, stderr: %s", stderr)
	}

	// 如果目标目录已存在 yum 目录，则跳过同步
	if strings.Contains(stdout, "yum_exists") {
		uc.log.Debugw("目标主机已存在yum目录，跳过同步", "targetDir", targetDir)
		return nil
	}

	// 确保目标目录存在
	mkdirCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'mkdir -p %s'",
		destPort, destHost, targetDir,
	)

	uc.log.Debugw("创建目标目录", "cmd", mkdirCmd, "targetDir", targetDir)

	// 执行创建目录命令
	_, stderr, done, sshErr = uc.cliSsh.Run(mkdirCmd, 1*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("SSH创建目录失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("创建目录超时或失败, stderr: %s", stderr)
	}

	// 在目标机器上执行 cp 命令，将 yum 源目录复制到目标目录
	// 注意：这里使用 cp 命令替代 rsync，以提高大文件传输速度
	cpCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'mkdir -p %s/yum && cp -a %s/* %s/yum/'",
		destPort, destHost, targetDir, yumSourcePath, targetDir,
	)

	uc.log.Debugw("开始复制yum源目录", "cmd", cpCmd, "sourceDir", yumSourcePath, "targetDir", targetDir)

	// 执行 cp 命令
	stdout, stderr, done, sshErr = uc.cliSsh.Run(cpCmd, 30*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("cp执行失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("cp复制超时或失败, stderr: %s", stderr)
	}

	uc.log.Debugw("yum源目录复制完成", "stdout", stdout, "stderr", stderr)

	// 检查并处理 /yum/jdstack/openEuler 目录
	openEulerPath := filepath.Join(targetDir, "yum", "jdstack", "openEuler")

	// 检查目录是否存在
	checkOpenEulerCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'if [ -d \"%s\" ]; then echo \"openeuler_exists\"; else echo \"openeuler_not_exists\"; fi'",
		destPort, destHost, openEulerPath,
	)

	uc.log.Debugw("检查目标主机是否存在openEuler目录", "cmd", checkOpenEulerCmd, "path", openEulerPath)

	// 执行检查命令
	stdout, stderr, done, sshErr = uc.cliSsh.Run(checkOpenEulerCmd, 1*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("SSH执行失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("检查openEuler目录超时或失败, stderr: %s", stderr)
	}

	// 如果目录存在，则处理它
	if strings.Contains(stdout, "openeuler_exists") {
		uc.log.Debugw("openEuler目录存在，开始处理", "path", openEulerPath)

		// 清空目录内容
		cleanCmd := fmt.Sprintf(
			"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'rm -rf %s/*'",
			destPort, destHost, openEulerPath,
		)

		uc.log.Debugw("清空openEuler目录", "cmd", cleanCmd)

		_, stderr, done, sshErr = uc.cliSsh.Run(cleanCmd, 5*time.Minute)
		if sshErr != nil {
			return fmt.Errorf("清空openEuler目录失败: %s, stderr: %s", sshErr.Error(), stderr)
		}
		if !done {
			return fmt.Errorf("清空openEuler目录超时或失败, stderr: %s", stderr)
		}
	} else {
		// 如果目录不存在，创建它
		mkdirCmd := fmt.Sprintf(
			"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'mkdir -p %s'",
			destPort, destHost, openEulerPath,
		)

		uc.log.Debugw("创建openEuler目录", "cmd", mkdirCmd)

		_, stderr, done, sshErr = uc.cliSsh.Run(mkdirCmd, 1*time.Minute)
		if sshErr != nil {
			return fmt.Errorf("创建openEuler目录失败: %s, stderr: %s", sshErr.Error(), stderr)
		}
		if !done {
			return fmt.Errorf("创建openEuler目录超时或失败, stderr: %s", stderr)
		}
	}

	// 获取版本号，从 data.Params.PackageVersion 中提取
	versionNumber := data.Params.PackageVersion

	// 创建版本号目录和所需的子目录结构
	createDirsCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'mkdir -p %s/%s/arm/jdstack_loader/basement/cvessel %s/%s/x86_64/jdstack_loader/basement/cvessel'",
		destPort, destHost, openEulerPath, versionNumber, openEulerPath, versionNumber,
	)

	uc.log.Debugw("创建版本号目录和子目录结构", "cmd", createDirsCmd, "version", versionNumber)

	_, stderr, done, sshErr = uc.cliSsh.Run(createDirsCmd, 2*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("创建目录结构失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("创建目录结构超时或失败, stderr: %s", stderr)
	}

	// 创建latest软链接指向版本号目录
	createLinkCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'cd %s && ln -sfn %s latest'",
		destPort, destHost, openEulerPath, versionNumber,
	)

	uc.log.Debugw("创建latest软链接", "cmd", createLinkCmd, "target", versionNumber)

	_, stderr, done, sshErr = uc.cliSsh.Run(createLinkCmd, 1*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("创建软链接失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("创建软链接超时或失败, stderr: %s", stderr)
	}
	uc.log.Debugw("openEuler目录处理完成", "path", openEulerPath, "version", versionNumber)

	// 创建 info 文件记录同步信息
	infoContent := fmt.Sprintf("操作时间: %s\\n源路径: %s\\n",
		time.Now().Format("2006-01-02 15:04:05"),
		yumSourcePath)

	infoFile := filepath.Join(targetDir, "yum", "info")

	// 构建创建info文件的SSH命令
	infoCmd := fmt.Sprintf(
		"ssh -i ~/.ssh/rsync_gen_offline_package -p %s %s 'echo -e \"%s\" > %s && echo \"info_created\" || echo \"info_failed\"'",
		destPort, destHost, infoContent, infoFile,
	)

	uc.log.Debugw("开始在目标主机创建yum info文件", "cmd", infoCmd, "infoFile", infoFile)

	// 执行创建info文件命令
	stdout, stderr, done, sshErr = uc.cliSsh.Run(infoCmd, 1*time.Minute)
	if sshErr != nil {
		return fmt.Errorf("SSH执行失败: %s, stderr: %s", sshErr.Error(), stderr)
	}
	if !done {
		return fmt.Errorf("创建info文件超时或失败, stderr: %s", stderr)
	}

	// 检查info文件是否创建成功
	if !strings.Contains(stdout, "info_created") {
		return fmt.Errorf("info文件创建失败，stdout: %s, stderr: %s", stdout, stderr)
	}

	uc.log.Debugw("yum info文件创建完成", "stdout", stdout, "stderr", stderr)

	return nil
}

// getIncrementalFilecenters 获取增量包需要的文件列表
func (uc *jdstackFilecenterUsecase) getIncrementalFilecenters(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams, errorCollection *ErrorCollection) ([]*dao.GoodsFilecenter, error) {
	// 查询目标版本的 goods
	targetNodes, err := uc.GoodsRepo.List(ctx, -1, 1, winapi.Filters{
		{
			Name:   winapi.MakeFilterName(goods.FilterHasJdstackReleaseWith, metadataservice.FieldName),
			Values: []string{data.Params.PackageVersion},
		},
		{
			Name:   goods.FilterWithGoodsFilecenter,
			Values: []string{},
		},
		{
			Name:   goods.FieldAction,
			Values: []string{"online"},
		},
		{
			Name:   winapi.MakeFilterName(goods.FilterWithGoodsFilecenter, goodsfilecenter.FieldArch),
			Values: []string{doJDStackFilecenter.Jdock2Arch(data.Params.Architecture)},
		},
		{
			Name:     winapi.MakeFilterName(goods.FilterWithGoodsFilecenter, goodsfilecenter.FieldHostOsType),
			Operator: winapi.OpIN,
			Values:   doJDStackFilecenter.Jdock2Oses(data.Params.HostOsTypes),
		},
	}, nil)

	if err != nil {
		errorCollection.AddError(0, "", fmt.Sprintf("查询目标版本goods失败: %s", err.Error()))
		return nil, err
	}

	// 查询基准版本的 goods
	baseNodes, err := uc.GoodsRepo.List(ctx, -1, 1, winapi.Filters{
		{
			Name:   winapi.MakeFilterName(goods.FilterHasJdstackReleaseWith, metadataservice.FieldName),
			Values: []string{data.Params.BaseVersion},
		},
		{
			Name:   goods.FilterWithGoodsFilecenter,
			Values: []string{},
		},
		{
			Name:   goods.FieldAction,
			Values: []string{"online"},
		},
		{
			Name:   winapi.MakeFilterName(goods.FilterWithGoodsFilecenter, goodsfilecenter.FieldArch),
			Values: []string{doJDStackFilecenter.Jdock2Arch(data.Params.Architecture)},
		},
		{
			Name:     winapi.MakeFilterName(goods.FilterWithGoodsFilecenter, goodsfilecenter.FieldHostOsType),
			Operator: winapi.OpIN,
			Values:   doJDStackFilecenter.Jdock2Oses(data.Params.HostOsTypes),
		},
	}, nil)

	if err != nil {
		errorCollection.AddError(0, "", fmt.Sprintf("查询基准版本goods失败: %s", err.Error()))
		return nil, err
	}

	// 创建基准版本文件路径映射，用于快速查找
	baseFilePaths := make(map[string]bool)
	for _, node := range baseNodes {
		for _, filecenter := range node.Edges.GoodsFilecenter {
			if filecenter.FilePath != nil {
				baseFilePaths[*filecenter.FilePath] = true
			}
		}
	}

	// 过滤出新增的制品
	var result []*dao.GoodsFilecenter
	for _, node := range targetNodes {
		for _, filecenter := range node.Edges.GoodsFilecenter {
			if filecenter.FilePath != nil && !baseFilePaths[*filecenter.FilePath] {
				// 这是一个新增的制品
				result = append(result, filecenter)
			}
		}
	}

	return result, nil
}

// getFullPackageFilecenters 获取全量包需要的文件列表
func (uc *jdstackFilecenterUsecase) getFullPackageFilecenters(ctx context.Context, data *doJDStackFilecenter.ModelFilecenterArtifactPackageTaskParams, errorCollection *ErrorCollection) ([]*dao.GoodsFilecenter, error) {
	// 查询所有制品
	nodes, err := uc.GoodsRepo.List(ctx, -1, 1, winapi.Filters{
		{
			Name:   winapi.MakeFilterName(goods.FilterHasJdstackReleaseWith, metadataservice.FieldName),
			Values: []string{data.Params.PackageVersion},
		},
		{
			Name:   goods.FilterWithGoodsFilecenter,
			Values: []string{},
		},
		{
			Name:   goods.FieldAction,
			Values: []string{"online"},
		},
		{
			Name:   winapi.MakeFilterName(goods.FilterWithGoodsFilecenter, goodsfilecenter.FieldArch),
			Values: []string{doJDStackFilecenter.Jdock2Arch(data.Params.Architecture)},
		},
		{
			Name:     winapi.MakeFilterName(goods.FilterWithGoodsFilecenter, goodsfilecenter.FieldHostOsType),
			Operator: winapi.OpIN,
			Values:   doJDStackFilecenter.Jdock2Oses(data.Params.HostOsTypes),
		},
	}, nil)

	if err != nil {
		errorCollection.AddError(0, "", fmt.Sprintf("查询goods失败: %s", err.Error()))
		return nil, err
	}

	// 收集所有文件中心数据
	var result []*dao.GoodsFilecenter
	for _, node := range nodes {
		result = append(result, node.Edges.GoodsFilecenter...)
	}

	return result, nil
}
