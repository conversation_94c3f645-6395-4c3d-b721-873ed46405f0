package bizVersion

import (
	"bufio"
	"context"
	"fmt"
	"os/exec"
	"strings"

	pbVersion "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/version/v1"
)

// CreateSqlExportToS3 导出指定版本的SQL文件并上传至S3
func (uc *versionUsecase) CreateSqlExportToS3(ctx context.Context, req *pbVersion.CreateSqlExportToS3Req) (*pbVersion.CreateSqlExportToS3Reply, error) {
	uc.log.Infof("开始导出SQL文件并上传至S3，版本号: %s, 是否携带前序版本: %t", req.Version, req.IncludePrevVersion)

	// 构建响应对象
	reply := &pbVersion.CreateSqlExportToS3Reply{
		Status:  "failed",
		Message: "",
	}

	// 脚本路径，这里先mock一个路径，后续可以通过配置文件获取
	scriptPath := uc.FilecenterConf.Cli["export_sql_shell_path"]
	user := uc.FilecenterConf.Cli["export_sql_user"]
	password := uc.FilecenterConf.Cli["export_sql_password"]
	if scriptPath == "" || user == "" || password == "" {
		err := fmt.Errorf("请检查zeus服务的配置文件，导出SQL文件脚本路径、用户名、密码不能为空")
		uc.log.Error(err)
		reply.Message = err.Error()
		return reply, err
	}

	// 构建命令参数
	args := []string{
		"-u", user,
		"-p", password,
	}

	// 如果需要包含前序版本数据，添加相应参数
	if req.IncludePrevVersion {
		args = append(args, "--with-prev")
	}

	// 添加S3相关参数
	args = append(args, "--split-insert") // 单表按数据拆分insert语句
	args = append(args, "-s")             // 启用S3上传,且使用默认配置
	args = append(args, "-r")             // 导出文件默认删除
	args = append(args, req.VersionType)  // 设置导出版本的版本类型（注意顺序，必须在version前）
	args = append(args, req.Version)      // 设置导出版本号

	uc.log.Infof("执行命令参数: %s %s", scriptPath, strings.Join(args, " "))

	// 构建命令
	cmd := exec.CommandContext(ctx, scriptPath, args...)

	// 创建管道获取标准输出
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		err = fmt.Errorf("创建标准输出管道失败: %v", err)
		uc.log.Error(err)
		reply.Message = err.Error()
		return reply, err
	}

	// 创建管道获取标准错误
	stderr, err := cmd.StderrPipe()
	if err != nil {
		err = fmt.Errorf("创建标准错误管道失败: %v", err)
		uc.log.Error(err)
		reply.Message = err.Error()
		return reply, err
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		err = fmt.Errorf("启动命令失败: %v", err)
		uc.log.Error(err)
		reply.Message = err.Error()
		return reply, err
	}

	// 读取标准输出
	s3Bucket := ""
	s3ObjectKey := ""
	scanner := bufio.NewScanner(stdout)
	for scanner.Scan() {
		line := scanner.Text()
		uc.log.Infof("\n\n脚本输出:\n%s", line)

		// 检查是否包含S3 Bucket信息
		if strings.HasPrefix(line, "S3_BUCKET=") {
			s3Bucket = strings.TrimPrefix(line, "S3_BUCKET=")
		}

		// 检查是否包含S3 Object Key信息
		if strings.HasPrefix(line, "S3_OBJECT_KEY=") {
			s3ObjectKey = strings.TrimPrefix(line, "S3_OBJECT_KEY=")
		}
	}

	// 读取标准错误
	errScanner := bufio.NewScanner(stderr)
	var errOutput strings.Builder
	for errScanner.Scan() {
		line := errScanner.Text()
		uc.log.Errorf("\n\n脚本错误输出:\n%s", line)
		errOutput.WriteString(line)
		errOutput.WriteString("\n")
	}

	// 等待命令完成
	err = cmd.Wait()
	if err != nil {
		err = fmt.Errorf("命令执行失败: %v, 错误输出: %s", err, errOutput.String())
		uc.log.Error(err)
		reply.Message = err.Error()
		return reply, err
	}

	// 检查是否获取到S3信息
	if s3Bucket == "" || s3ObjectKey == "" {
		errMsg := "未能从脚本输出中获取S3信息"
		if s3Bucket == "" {
			errMsg += ": 缺少S3 Bucket信息"
		}
		if s3ObjectKey == "" {
			errMsg += ": 缺少S3 Object Key信息"
		}
		err = fmt.Errorf(errMsg)
		uc.log.Error(err)
		reply.Message = errMsg
		return reply, err
	}

	// 设置响应信息
	reply.S3Bucket = s3Bucket
	reply.S3ObjectKey = s3ObjectKey
	reply.Status = "success"
	reply.Message = fmt.Sprintf("成功导出SQL文件并上传至S3: %s/%s", s3Bucket, s3ObjectKey)
	uc.log.Infof("SQL文件导出并上传至S3成功: %s/%s", s3Bucket, s3ObjectKey)

	return reply, nil
}
