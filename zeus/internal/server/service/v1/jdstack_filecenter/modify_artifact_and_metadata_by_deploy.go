package servJDStackFilecenter

import (
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/jdstackrelease"
	"context"
	"encoding/json"
	"time"

	jPolicy "coding.jd.com/pcd-application/win-go/biz_policy"
	"github.com/gogf/gf/v2/util/gconv"

	jContext "coding.jd.com/pcd-application/win-go/context"
	jErr "coding.jd.com/pcd-application/win-go/error"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"

	pbGoodsStack "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1"
	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
	pbMetadata "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/metadata/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goods"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodsfilecenter"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
)

// ModifyArtifactAndMetadataByDeploy 根据部署更新制品和应用元数据
func (uc *jdstackFilecenterServer) ModifyArtifactAndMetadataByDeploy(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.ModifyArtifactAndMetadataByDeployReq) (*pbJdstackFilecenter.DescribeArtifactAndMetadataBySealReply, error) {
	PackagePath := "/export/Data/JDStack_File" // todo-getconfig
	policy := jPolicy.NewPolicyStatus(req.Policy)
	// 处理单个应用的制品信息
	filecenterReqs := []*pbGoodsStack.CreateFilecenterReq{}
	for _, artifact := range req.AppInfo.Artifacts {
		packageFilePath := doJDStackFilecenter.Jdock2Arch(artifact.Arch) + "/" + doJDStackFilecenter.Jdock2FileType(artifact.FileType) + "/" + req.ServiceCode + "/" + artifact.FileName
		// fullPath := PackagePath + "/" + packageFilePath

		// // 检查文件是否存在
		// if _, err := os.Stat(fullPath); err != nil {
		// 	// 文件不存在,执行下载
		// 	downloadFile, err := uc.JDStackFilecenterCase.DoDownload2PackageFilePath(ctx, &doJDStackFilecenter.ModelDownload2PackageFilePathReq{
		// 		PackagePath: PackagePath,
		// 		PackageFilePath: &doJDStackFilecenter.ModelPackageFilePathReq{
		// 			ServiceCode:   req.ServiceCode,
		// 			FileType:      artifact.FileType,
		// 			Arch:          doJDStackFilecenter.Jdock2Arch(artifact.Arch),
		// 			Filename:      artifact.FileName,
		// 			DirectorySpec: goodsfilecenter.DirectorySpecArchFileTypeServiceCode.String(),
		// 		},
		// 		Address: artifact.Address,
		// 		Stage:   doJDStackFilecenter.GetDownloadStage(doJDStackFilecenter.Jdock2FileType(artifact.FileType)),
		// 	})
		// 	if err != nil {
		// 		return nil, err
		// 	}
		// 	if !downloadFile.Success {
		// 		return nil, fmt.Errorf("download failed: %v", downloadFile)
		// 	}
		// }

		filecenterReq := &pbGoodsStack.CreateFilecenterReq{
			Arch:           doJDStackFilecenter.Jdock2Arch(artifact.Arch),
			HostOsType:     doJDStackFilecenter.Jdock2Os(artifact.Os),
			FileIdentifier: artifact.Identifier,
			DirectorySpec:  goodsfilecenter.DirectorySpecArchFileTypeServiceCode.String(),
			FileName:       artifact.FileName,
			FileHash:       artifact.Md5,
			ImageTags:      artifact.ImageTag,
			FileVersion:    artifact.Version,
			FileType:       doJDStackFilecenter.Jdock2FileType(artifact.FileType),
			FilePath:       PackagePath + "/" + packageFilePath,
		}
		if policy.OnOverrideAction {
			filecenterReq.Policy = []string{"override"}
		}
		paramMap := doJDStackFilecenter.ToPartialJSONWithMap(*artifact,
			doJDStackFilecenter.JdockParam, doJDStackFilecenter.JdockParamMap)
		if doJDStackFilecenter.Jdock2FileType(artifact.FileType) == doJDStackFilecenter.FileTypeDockerImage {
			if req.AppInfo.DeployMode == doJDStackFilecenter.DeployModeSkywingContainer {
				paramMap["type"] = "skywing_image"
			} else {
				paramMap["type"] = "docker_image"
			}
			paramMap["registry"] = artifact.ImageRepo
		}
		if doJDStackFilecenter.Jdock2FileType(artifact.FileType) == doJDStackFilecenter.FileTypePackage {
			if req.AppInfo.DeployMode == doJDStackFilecenter.DeployModeSkywingPackage {
				paramMap["type"] = "skywing_package"
			} else {
				paramMap["type"] = "xingyun_package"
			}
		}
		if doJDStackFilecenter.Jdock2FileType(artifact.FileType) == doJDStackFilecenter.FileTypeRegularFile {
			paramMap["region"] = artifact.Md5
		}
		paramBytes, _ := json.Marshal(paramMap)
		filecenterReq.Param = gconv.String(paramBytes)
		filecenterReqs = append(filecenterReqs, filecenterReq)
	}

	autoOffline := false
	for _, p := range req.Policy {
		if p == "autoOfflineOthers" {
			autoOffline = true
		}
	}
	// 【新增】查询同一application关联的其他goods并将它们置为下线
	if req.AppInfo.AppCode != "" && autoOffline {
		// 查询同一application关联的所有goods
		existingGoods, err := uc.queryExistingApplicationGoods(ctx, req.AppInfo.AppCode, req.CloudType, req.Version)
		if err != nil {
			return nil, jErr.NewMessage(Self, "查询应用关联的goods失败").AddError(err)
		}

		// 将不匹配当前resource/service/category组合的goods置为下线
		for _, existingGood := range existingGoods {
			// 跳过与当前请求匹配的goods
			if existingGood.Resource == req.ProductCode &&
				existingGood.Service == req.ServiceCode &&
				existingGood.Category == req.Category {
				continue
			}

			// 将其他goods置为下线
			err := uc.GoodsStackCase.Create(ctx, &pbGoodsStack.CreateReq{
				Version:     req.Version,
				Policy:      []string{"override"},
				Category:    existingGood.Category,
				Service:     existingGood.Service,
				Resource:    existingGood.Resource,
				Application: req.AppInfo.AppCode,
				Action:      "offline", // 设置为下线
				CloudType:   req.CloudType,
			})

			if err != nil {
				return nil, jErr.NewMessage(Self, "下线其他goods失败").WithData(err)
			}
		}
	}

	// 创建/更新应用
	err := uc.GoodsStackCase.Create(ctx, &pbGoodsStack.CreateReq{
		Version: req.Version,
		Policy:  []string{"override", "foreignKeyDiscarded", "FKD:GoodsFilecenter"},
		Metadata: &pbMetadata.CreateReq{
			Application: &pbMetadata.CreateApplicationReq{
				Name:                           req.AppInfo.AppCode,
				NameCn:                         req.AppInfo.AppName,
				DeployMode:                     req.AppInfo.DeployMode,
				CloudType:                      req.CloudType,
				HighAvailabilityDesignPatterns: "",
			},
		},
		Category:     req.Category,
		Service:      req.ServiceCode,
		Resource:     req.ProductCode,
		Application:  req.AppInfo.AppCode,
		Action:       "online",
		VirtualGoods: req.AppInfo.VirtualGoods,
		CloudType:    req.CloudType,
		Filecenter:   filecenterReqs,
	})
	if err != nil {
		return nil, jErr.NewMessage(Self, "创建应用失败").WithData(err)
	}

	// 更新jdstack_release的更新时间，同时用作触发stack打包的时候检查是否有人更新了zeus(晚于导出的zeus.sql时间戳)
	filters := winapi.Filters{
		&winapi.Filter{
			Name:   jdstackrelease.FieldName,
			Values: []string{req.Version},
		},
		&winapi.Filter{
			Name:   jdstackrelease.FieldCloudType,
			Values: []string{req.CloudType},
		},
	}
	jdstackReleaseDao, jError := uc.JdstackReleaseRepo.GetName(ctx, req.Version, filters...)
	if jError != nil {
		jError = jError.AddReasonf("查询jdstack版本失败")
		return nil, jError
	}
	jdstackReleaseDao.UpdatedAt = time.Now().Round(time.Second)
	jError = uc.JdstackReleaseRepo.Update(ctx, jdstackReleaseDao)
	if jError != nil {
		jError = jError.AddReasonf("更新jdstack版本失败")
		return nil, jError
	}

	return &pbJdstackFilecenter.DescribeArtifactAndMetadataBySealReply{
		Success: true,
		Message: "同步完成",
	}, nil
}

// 【新增】辅助方法：查询同一application关联的所有goods
func (uc *jdstackFilecenterServer) queryExistingApplicationGoods(ctx context.Context, appCode string, cloudType string, version string) ([]struct {
	Category string
	Service  string
	Resource string
}, error) {
	// 使用winapi.Filters构建查询条件
	filters := winapi.Filters{
		&winapi.Filter{Name: "application", Values: []string{appCode}},
		&winapi.Filter{Name: "version", Values: []string{version}},
		&winapi.Filter{Name: goods.FieldCloudType, Values: []string{cloudType}},
		&winapi.Filter{Name: winapi.EmptyValue, Values: []string{
			goods.FilterWithMetadataCategory,
			goods.FilterWithMetadataService,
			goods.FilterWithMetadataResource,
		}},
	}

	// 查询goods
	goodsList, err := uc.GoodsRepo.List(ctx, 0, 0, filters, winapi.Sorts{})
	if err != nil {
		return nil, err
	}

	// 提取category、service和resource信息
	result := make([]struct {
		Category string
		Service  string
		Resource string
	}, 0, len(goodsList))

	for _, goodsDao := range goodsList {
		item := struct {
			Category string
			Service  string
			Resource string
		}{}

		if goodsDao.Edges.MetadataCategory != nil && goodsDao.Edges.MetadataCategory.NameCn != nil {
			item.Category = *goodsDao.Edges.MetadataCategory.NameCn
		}

		if goodsDao.Edges.MetadataService != nil && goodsDao.Edges.MetadataService.Name != nil {
			item.Service = *goodsDao.Edges.MetadataService.Name
		}

		if goodsDao.Edges.MetadataResource != nil && goodsDao.Edges.MetadataResource.Name != nil {
			item.Resource = *goodsDao.Edges.MetadataResource.Name
		}

		result = append(result, item)
	}

	return result, nil
}
