package servJDStackFilecenter

import (
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/jdstackrelease"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/util/gconv"

	jContext "coding.jd.com/pcd-application/win-go/context"
	jErr "coding.jd.com/pcd-application/win-go/error"
	"coding.jd.com/pcd-application/win-go/third_party/winapi"

	pbGoodsStack "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1"
	pbJdstackFilecenter "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1"
	pbMetadata "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/metadata/v1"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goods"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/goodsfilecenter"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadataresource"
	"coding.jd.com/fabric/zeusV2/zeus/internal/server/data/db_master/dao/metadataservice"
	doJDStackFilecenter "coding.jd.com/fabric/zeusV2/zeus/internal/server/domain/jdstack_filecenter"
)

// ModifyArtifactAndMetadataBySeal is a generated function.
func (uc *jdstackFilecenterServer) ModifyArtifactAndMetadataBySeal(ctx context.Context, c jContext.Context, req *pbJdstackFilecenter.ModifyArtifactAndMetadataBySealReq) (*pbJdstackFilecenter.DescribeArtifactAndMetadataBySealReply, error) {
	// 确认产品是否为虚拟产品
	isVirtual := "yes"
	for _, info := range req.AppInfos {
		if info.VirtualGoods == "no" {
			isVirtual = "no"
		}
	}
	// FIXME: 云舰类型暂时仅更新产品级别，且默认设置为非虚拟
	if req.CloudType == goods.CloudTypeCvessel.String() {
		isVirtual = "no"
	}

	// 创建/更新产品 - 更新商业平台元数据 & 更新产品pd
	// HACK: (2025.0429更新)当云舰产品同步元数据到zeus的时候(从选品环境抽取商业平台元数据在同步到zeus)，只会将数据初始化到zeus的云舰版本中，
	// 从而能将商业平台元数据存储到unified_metadata中，方便在线调试foundation_init能从中心zeus获取元数据
	// 后续当从天基云舰版本将封版快照同步到zeus的时候，会重新将全部商业平台元数据初始化一遍
	err := uc.GoodsStackCase.Create(ctx, &pbGoodsStack.CreateReq{
		Version: req.Version,
		Policy:  []string{"override"},
		Metadata: &pbMetadata.CreateReq{
			Category: &pbMetadata.CreateCategoryReq{
				NameCn: req.Category,
			},
			Service: &pbMetadata.CreateServiceReq{
				Name:   req.ServiceCode,
				NameCn: req.ServiceCode,
			},
			Resource: &pbMetadata.CreateResourceReq{
				Name:   req.ProductCode,
				NameCn: req.ProductName,
				Scope:  "global", // todo-先默认
			},
		},
		Category:     req.Category,
		Service:      req.ServiceCode,
		Resource:     req.ProductCode,
		Application:  "",
		Action:       "online",
		VirtualGoods: isVirtual,
		CloudType:    req.CloudType,
		Pd: &pbGoodsStack.CreatePdReq{
			Content: req.ProductYaml,
		},
		BusinessMetadata: &pbGoodsStack.CreateMetadataReq{
			UnifiedMetadata: req.BusinessMetadata,
		},
	})
	if err != nil {
		return nil, jErr.NewMessage(Self, "创建产品失败").WithData(err)
	}

	// FIXME: 云舰类型暂时仅更新resource维度，不处理应用级别
	if req.CloudType == goods.CloudTypeCvessel.String() {
		return &pbJdstackFilecenter.DescribeArtifactAndMetadataBySealReply{
			Success: true,
			Message: "同步完成",
		}, nil
	}

	// 查询出产品下应用goods列表
	oldAppGoods, err := uc.GoodsRepo.List(ctx, -1, 1, winapi.Filters{
		{
			Name:   winapi.MakeFilterName(goods.FilterHasMetadataResourceWith, metadataresource.FieldName),
			Values: []string{req.ProductCode},
		},
		{
			Name:   winapi.MakeFilterName(goods.FilterHasJdstackReleaseWith, metadataservice.FieldName),
			Values: []string{req.Version},
		},
		{Name: winapi.EmptyValue, Values: goods.ColumnsFilterWith},
		{
			Name:     goods.FieldApplicationID,
			Operator: winapi.OpNEQ,
			Values:   []string{"0"},
		},
		{
			Name:   goods.FieldAction,
			Values: []string{"online"},
		},
	}, nil)
	if err != nil {
		return nil, err
	}
	PackagePath := "/export/Data/JDStack_File" // todo-getconfig
	// 创建/更新应用
	for _, info := range req.AppInfos {
		filecenterReqs := []*pbGoodsStack.CreateFilecenterReq{}
		for _, artifact := range info.Artifacts {
			packageFilePath := doJDStackFilecenter.Jdock2Arch(artifact.Arch) + "/" + doJDStackFilecenter.Jdock2FileType(artifact.FileType) + "/" + req.ServiceCode + "/" + artifact.FileName
			// fullPath := PackagePath + "/" + packageFilePath

			// FIXME: 这里引入bug，导致3.44机器上的部分制品文件成了file_name-{md5}-{md5}的格式,禁止启用
			// 检查文件是否存在
			// if _, err := os.Stat(fullPath); err != nil {
			// 	// 文件不存在,执行下载
			// 	downloadFile, err := uc.JDStackFilecenterCase.DoDownload2PackageFilePath(ctx, &doJDStackFilecenter.ModelDownload2PackageFilePathReq{
			// 		PackagePath: PackagePath,
			// 		PackageFilePath: &doJDStackFilecenter.ModelPackageFilePathReq{
			// 			ServiceCode:   req.ServiceCode,
			// 			FileType:      artifact.FileType,
			// 			Arch:          doJDStackFilecenter.Jdock2Arch(artifact.Arch),
			// 			Filename:      artifact.FileName,
			// 			DirectorySpec: goodsfilecenter.DirectorySpecArchFileTypeServiceCode.String(),
			// 		},
			// 		Address: artifact.Address,
			// 		Stage:   doJDStackFilecenter.GetDownloadStage(doJDStackFilecenter.Jdock2FileType(artifact.FileType)),
			// 	})
			// 	if err != nil {
			// 		return nil, err
			// 	}
			// 	if !downloadFile.Success {
			// 		return nil, fmt.Errorf("download failed: %v", downloadFile)
			// 	}
			// }

			filecenterReq := &pbGoodsStack.CreateFilecenterReq{
				Policy:         []string{"override"},
				Arch:           doJDStackFilecenter.Jdock2Arch(artifact.Arch),
				HostOsType:     doJDStackFilecenter.Jdock2Os(artifact.Os),
				FileIdentifier: artifact.Identifier,
				DirectorySpec:  goodsfilecenter.DirectorySpecArchFileTypeServiceCode.String(),
				FileName:       artifact.FileName,
				FileHash:       artifact.Md5,
				ImageTags:      artifact.ImageTag,
				FileVersion:    artifact.Version,
				FileType:       doJDStackFilecenter.Jdock2FileType(artifact.FileType),
				FilePath:       PackagePath + "/" + packageFilePath,
			}
			paramMap := doJDStackFilecenter.ToPartialJSONWithMap(*artifact,
				doJDStackFilecenter.JdockParam, doJDStackFilecenter.JdockParamMap)
			if doJDStackFilecenter.Jdock2FileType(artifact.FileType) == doJDStackFilecenter.FileTypeDockerImage {
				if info.DeployMode == doJDStackFilecenter.DeployModeSkywingContainer {
					paramMap["type"] = "skywing_image"
				} else {
					paramMap["type"] = "docker_image"
				}
				paramMap["registry"] = artifact.ImageRepo
			}
			if doJDStackFilecenter.Jdock2FileType(artifact.FileType) == doJDStackFilecenter.FileTypePackage {
				if info.DeployMode == doJDStackFilecenter.DeployModeSkywingPackage {
					paramMap["type"] = "skywing_package"
				} else {
					paramMap["type"] = "xingyun_package"
				}
				// paramMap["registry"] = artifact.ImageRepo
			}
			if doJDStackFilecenter.Jdock2FileType(artifact.FileType) == doJDStackFilecenter.FileTypeRegularFile {
				paramMap["region"] = artifact.Md5
			}
			paramBytes, _ := json.Marshal(paramMap)
			filecenterReq.Param = gconv.String(paramBytes)
			filecenterReqs = append(filecenterReqs, filecenterReq)
		}
		err = uc.GoodsStackCase.Create(ctx, &pbGoodsStack.CreateReq{
			Version: req.Version,
			Policy:  []string{"override", "foreignKeyDiscarded", "FKD:GoodsFilecenter"},
			Metadata: &pbMetadata.CreateReq{
				Application: &pbMetadata.CreateApplicationReq{
					Name:                           info.AppCode,
					NameCn:                         info.AppName,
					DeployMode:                     info.DeployMode,
					CloudType:                      req.CloudType,
					HighAvailabilityDesignPatterns: "",
				},
			},
			Category:     req.Category,
			Service:      req.ServiceCode,
			Resource:     req.ProductCode,
			Application:  info.AppCode,
			Action:       "online",
			VirtualGoods: info.VirtualGoods,
			CloudType:    req.CloudType,
			Filecenter:   filecenterReqs,
		})
		if err != nil {
			return nil, jErr.NewMessage(Self, "创建应用失败").WithData(err)
		}
	}
	for _, goodsDao := range oldAppGoods {
		needOffline := true
		for _, info := range req.AppInfos {
			if *goodsDao.Edges.MetadataApplication.Name == info.AppCode {
				needOffline = false
			}
		}
		if needOffline {
			err = uc.GoodsStackCase.Create(ctx, &pbGoodsStack.CreateReq{
				Version:      *goodsDao.Edges.JdstackRelease.Name, // todo-区分云舰
				Policy:       []string{"override"},
				Application:  *goodsDao.Edges.MetadataApplication.Name,
				Resource:     req.ProductCode,
				Service:      req.ServiceCode,
				Category:     req.Category,
				Action:       "offline",
				VirtualGoods: goodsDao.VirtualGoods.String(),
				CloudType:    goodsDao.CloudType.String(),
			})
			if err != nil {
				return nil, jErr.NewMessage(Self, fmt.Sprintf("下线应用失败，应用：%s", *goodsDao.Edges.MetadataApplication.Name)).WithData(err)
			}
		}
	}

	// 更新jdstack_release的更新时间，同时用作触发stack打包的时候检查是否有人更新了zeus(晚于导出的zeus.sql时间戳)
	filters := winapi.Filters{
		&winapi.Filter{
			Name:   jdstackrelease.FieldName,
			Values: []string{req.Version},
		},
		&winapi.Filter{
			Name:   jdstackrelease.FieldCloudType,
			Values: []string{req.CloudType},
		},
	}
	jdstackReleaseDao, jError := uc.JdstackReleaseRepo.GetName(ctx, req.Version, filters...)
	if jError != nil {
		jError = jError.AddReasonf("查询jdstack版本失败")
		return nil, jError
	}
	jdstackReleaseDao.UpdatedAt = time.Now().Round(time.Second)
	jError = uc.JdstackReleaseRepo.Update(ctx, jdstackReleaseDao)
	if jError != nil {
		jError = jError.AddReasonf("更新jdstack版本失败")
		return nil, jError
	}

	// 旧应用列表中goodid不在请求列表中，进行下线操作
	return &pbJdstackFilecenter.DescribeArtifactAndMetadataBySealReply{
		Success: true,
		Message: "同步完成",
	}, nil
}
