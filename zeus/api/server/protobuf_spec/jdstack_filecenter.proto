syntax = "proto3";

package jdstack.zeus.v1.jdstackFilecenter;

option go_package = "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/jdstack_filecenter/v1;pbJdstackFilecenter";

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "validate/validate.proto";
import "goods_stack.proto";
import "unified_metadata.proto";
import "google/protobuf/empty.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  host: "localhost:35711"
  base_path: "/v1"
  schemes: [HTTP]
  info: {
    title: "JDStackFilecenter_JDStack文件管理"
    version: "v1"
  }
};

service JdstackFilecenter {
  rpc DescribeDeploymentServerInfo(google.protobuf.Empty) returns (DeploymentServerInfoReply) {
    option (google.api.http) = {get: "/filecenter:DeploymentServerInfo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取部署服务器信息"
    };
  }

  rpc CreateFileDownloadTask(CreateFileDownloadReq) returns (DescribeTaskReply) {
    option (google.api.http) = {post: "/filecenter:download", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "下载文件到公共目录（异步接口-创建）"
    };
  }
  rpc DescribeFileDownloadTask(DescribeTaskReq) returns (DescribeFileDownloadReply) {
    option (google.api.http) = {get: "/filecenter:queryDownload"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "下载文件到公共目录（异步接口-查询）"
    };
  }
  rpc CreateAppFilesPushTask(AppFilesTaskReq) returns (DescribeTaskReply) {
    option (google.api.http) = {post: "/filecenter:pushAppFile", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "推送产品所有制品到环境仓库（异步接口-创建）"
    };
  }
  rpc DescribeAppFilesPushTask(DescribeTaskReq) returns (DescribeAppFileTaskReply) {
    option (google.api.http) = {get: "/filecenter:queryPushAppFile"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询推送应用所有制品到环境仓库（异步接口-查询）"
    };
  }
  rpc ModifyArtifactAndMetadataBySeal(ModifyArtifactAndMetadataBySealReq) returns (DescribeArtifactAndMetadataBySealReply) {
    option (google.api.http) = {post: "/version/{version}/filecenter:updateBySeal", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "根据封板更新制品和产品应用元数据"
    };
  }
  
  rpc ModifyArtifactAndMetadataByDeploy(ModifyArtifactAndMetadataByDeployReq) returns (DescribeArtifactAndMetadataBySealReply) {
    option (google.api.http) = {post: "/version/{version}/filecenter:updateByDeploy", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "根据部署更新制品和应用元数据"
    };
  }
  rpc DescribesArtifact(DescribesArtifactReq) returns (DescribesArtifactReply) {
    option (google.api.http) = {post: "/version/{version}/filecenter:artifacts", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "根据版本和应用返回制品列表"
    };
  }
  rpc CreateArtifactPackageTask(CreateArtifactPackageReq) returns (DescribeTaskReply) {
    option (google.api.http) = {post: "/version/{packageVersion}/filecenter:exportArtifactPackage", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "导出制品离线包（异步接口-创建）"
    };
  }
  rpc DescribeArtifactPackageTask(DescribeTaskReq) returns (DescribeArtifactPackageReply) {
    option (google.api.http) = {get: "/filecenter:queryArtifactPackage"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "导出制品离线包（异步接口-查询）"
    };
  }
  rpc CreateSCFilesPushTask(CreateSCFilesTaskReq) returns (DescribeTaskReply) {
    option (google.api.http) = {post: "/filecenter:pushAllFile", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "推送sc所有制品到环境仓库（异步接口-创建）"
    };
  }
  rpc DescribeSCFilesPushTask(DescribeTaskReq) returns (DescribeSCFilesResponseData) {
    option (google.api.http) = {get: "/filecenter:queryPushAllFile"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询推送sc所有制品到环境仓库（异步接口-查询）"
    };
  }

  rpc CreateUploadTask(CreateUploadTaskReq) returns (jdstack.zeus.v1.goodsStack.DescribesFilecenterReply) {
    option (google.api.http) = {post: "/version/{version}/filecenter:uploadTask", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "上传文件或镜像"
    };
  }
  rpc CreateArtifact(CreateArtifactReq) returns (DescribeCommonReply) {
    option (google.api.http) = {post: "/version/{version}/filecenter:artifact", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建制品信息"
    };
  }

  rpc CreateRegularFilePushTask(RegularFileTaskReq) returns (DescribeCommonReply) {
    option (google.api.http) = {post: "/filecenter:pushRegularFile", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "推送普通文件制品到环境仓库（异步接口-创建）"
    };
  }
  rpc CreateRpmPushTask(RpmTaskReq) returns (DescribeCommonReply) {
    option (google.api.http) = {post: "/filecenter:pushRpm", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "推送rpm制品到环境仓库（异步接口-创建）"
    };
  }
  rpc CreateSkywingPackagePushTask(SkywingPackageTaskReq) returns (DescribeCommonReply) {
    option (google.api.http) = {post: "/filecenter:pushSkywingPackage", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "推送云翼包制品到环境仓库（异步接口-创建）"
    };
  }
  rpc CreateDockerImagePushTask(DockerImageTaskReq) returns (DescribeCommonReply) {
    option (google.api.http) = {post: "/filecenter:pushDockerImage", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "推送docker镜像到环境仓库（异步接口-创建）"
    };
  }
  rpc DescribeRegularFilePushTask(DescribeTaskReq) returns (DescribeCommonFileTaskReply) {
    option (google.api.http) = {get: "/filecenter:queryPushRegularFile"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询推送普通文件制品到环境仓库（异步接口-查询）"
    };
  }
  rpc DescribeRpmPushTask(DescribeTaskReq) returns (DescribeCommonFileTaskReply) {
    option (google.api.http) = {get: "/filecenter:queryPushRpm"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询推送rpm制品到环境仓库（异步接口-查询）"
    };
  }
  rpc DescribeSkywingPackagePushTask(DescribeTaskReq) returns (DescribeCommonFileTaskReply) {
    option (google.api.http) = {get: "/filecenter:queryPushSkywingPackage"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询推送云翼包制品到环境仓库（异步接口-查询）"
    };
  }
  rpc DescribeDockerImagePushTask(DescribeTaskReq) returns (DescribeCommonFileTaskReply) {
    option (google.api.http) = {get: "/filecenter:queryPushDockerImage"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "查询推送docker镜像到环境仓库（异步接口-查询）"
    };
  }

  rpc CreateMetadataPackage(CreateMetadataPackageReq) returns (DescribeCommonReply) {
    option (google.api.http) = {post: "/version/{packageVersion}/filecenter:exportMetadataPackage", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "导出封板元数据（异步接口-创建）"
    };
  }
  rpc DescribeMetadataPackage(DescribeTaskReq) returns (DescribeArtifactPackageReply) {
    option (google.api.http) = {get: "/filecenter:queryMetadataPackage"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "导出封板元数据（异步接口-查询）"
    };
  }
}

message FilePropertiesRes {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["identifier", "filetype", "address"]}};
  string identifier = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件标识\n- 等于上级appName或自定义，相同上级、架构、系统的文件标识不得重复"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string deployMode = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署模式:\n[ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]"},
    (validate.rules).string = {in: "skywing_container", in: "skywing_package", in: "mate_container", in: "shell", in: "helm", in: "rpm", in: "mate_bin", in: "mate_v2", in: "iaas_image", in: "xingyun_container", in: "xingyun_package"}
  ];
  string filename = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称,默认从地址内提取"}
  ];
  string address = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像、包、文件源地址"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string moduleName = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "模块名\n- 文件类型 [ skywing_package image ] 必填"}
  ];
  string imageTags = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像版本,默认从地址内提取"}
  ];
  string storageMode = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包存储位置类型\n- 文件类型 [ s3 ] 必填: [ http minio ]"}
  ];
  string storageBucket = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包存储位置\n- 文件类型 [ s3 ] 必填"}
  ];
  string repoKind = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "仓库名称\n- 文件类型 [ rpm ] 必填可选: [ release iaas-release oe-release ... ]"}
  ];
  string commitId = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联代码仓库的CommitID"}
  ];
  string os = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统:\n[ centos openEuler kylin]"},
    (validate.rules).string = {ignore_empty: true, in: "centos", in: "openEuler", in: "kylin"}
  ];
  string arch = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ x86_64 arm ]"},
    (validate.rules).string = {in: "x86_64", in: "arm"}
  ];
}

message CreateUploadTaskReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["version", "serviceCode", "arch", "os", "files"]}};
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z.]+$"}
  ];
  repeated string policy = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同serviceCode、appName、arch、os、identifier文件信息"}
  ];
  bool isStore = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否要将文件或镜像信息落库"}
  ];
  string serviceCode = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品名称"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string appName = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用名称"}
  ];
  string arch = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ x86_64 arm ]"},
    (validate.rules).string = {in: "x86_64", in: "arm"}
  ];
  string os = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统:\n[ centos openEuler kylin]"},
    (validate.rules).string = {in: "centos", in: "openEuler", in: "kylin"}
  ];
  repeated FilePropertiesRes files = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件信息"},
    (validate.rules).repeated = {min_items: 1}
  ];
  string envName = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传环境名称,需Filecenter脚本支持,默认空=仅改Filecenter不往环境推送"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z.-_]+$"}
  ];
}
message CreateFileDownloadReq{
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema:{required: ["filePath","serviceCode","arch","fileType"]}};
  string filePath = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件路径"}
  ];
  string serviceCode = 2[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "serviceCode"}
  ];
  string arch = 3[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ amd64 arm64 ]"},
    (validate.rules).string = {in: "amd64", in: "arm64"}
  ];
  string fileType = 4[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件类型:\n[ image out_image os_file rpm package helm ]"},
    (validate.rules).string = {in: "image", in: "out_image", in: "rpm", in: "os_file", in: "package", in: "helm"}
  ];
  string appVersionID = 5[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用版本id"}
  ];
}
message DescribeFileDownloadReply{
  string status = 1[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务状态"}
  ];
  string fileName = 2[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称"}
  ];
  string md5 = 3[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "md5"}
  ];
  string errorMsg = 4[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "错误信息"}
  ];
}
message ModifyArtifactAndMetadataByDeployReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema:{required: ["version"]}};
  string cloudType = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "平台类型"}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z.]+$"}
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同serviceCode、appName、arch、os、identifier文件信息 \nautoOfflineOthers - 自动下线其他关联"}
  ];
  string serviceCode = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "serviceCode"}
  ];
  string category = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类名称"}
  ];
  string productCode = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品Code"}
  ];
  string productName = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品名称"}
  ];
  ApplicationInfoReq appInfo = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用信息"}
  ];
}

message ModifyArtifactAndMetadataBySealReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema:{required: ["version"]}};
  string cloudType = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "平台类型"}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z.]+$"}
  ];
  repeated string policy = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "策略:\noverride - 覆盖相同serviceCode、appName、arch、os、identifier文件信息"}
  ];
  string serviceCode = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "serviceCode"}
  ];
  string category = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "分类名称"}
  ];
  string productCode = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品Code"}
  ];
  string productName = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品名称"}
  ];
  repeated ApplicationInfoReq appInfos = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用信息"}
  ];
  string productYaml = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "pd描述"}
  ];
  repeated goodsStack.AdlReq adlFiles = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品级别的adl文件"}
  ];
  unifiedMetadata.CreateReq businessMetadata = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品关联的的商业平台元数据信息"}
  ];
}

message DescribeArtifactAndMetadataBySealReply {
  bool success = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作是否成功"}
  ];

  string message = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回信息"}
  ];
}
message ApplicationInfoReq {
  string appCode = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用Code"}
  ];
  string appName = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用Code"}
  ];
  string deployMode = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署模式:\n[ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]"},
    (validate.rules).string = {in: "skywing_container", in: "skywing_package", in: "mate_container", in: "shell", in: "helm", in: "rpm", in: "mate_bin", in: "mate_v2", in: "iaas_image", in: "xingyun_container", in: "xingyun_package"}
  ];
  string virtualGoods = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "虚拟应用:\n[ no yes ]", default: "no"},
    (validate.rules).string = {ignore_empty: true, in: "no", in: "yes"}
  ];
  repeated ArtifactReq artifacts = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "制品信息"}
  ];
  string appYaml = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用配置"}
  ];
  repeated goodsStack.AdlReq adlFiles = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用级别的adl文件"}
  ];
}

message ArtifactReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["identifier", 'version', "filetype", "address", 'arch', 'os']}};
  string identifier = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件标识\n- 等于上级appName或自定义，相同上级、架构、系统的文件标识不得重复"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件版本"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string fileType = 3[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件类型:\n[ image out_image os_file rpm package helm ]"},
    (validate.rules).string = {in: "image", in: "out_image", in: "rpm", in: "os_file", in: "package", in: "helm"}
    ];
  string fileName = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称"}
  ];
  string md5 = 5[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "md5"}
  ];
  string address = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像、包、文件源地址"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string fileStorageMode = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储位置类型\n [ http minio ]"}
  ];
  string fileBucket = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储bucket"}
  ];
  string rpmRepo = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "rpm仓库名称\n [ release iaas-release oe-release]"}
  ];
  string imageRepo = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像repo"}
  ];
  string imageTag = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像tag"}
  ];
  string pkgModelName = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包模块名称"}
  ];
  string pkgVersion = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包版本"}
  ];
  string os = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统"},
    (validate.rules).string = {in: ["centos", "openeuler22", "kylinv10"]}
  ];
  string arch = 15 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ amd64 arm64 ]"},
    (validate.rules).string = {in: "amd64", in: "arm64"}
  ];
}
message RegularFileTaskReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["serviceCode", "filename", "moduleName", "packageVersion", "appName", "md5", "architecture", "version", "region", "hostOsType"]}};
  string appName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件对应的app名称"}
  ];

  string architecture = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ amd64 arm64 ]"},
    (validate.rules).string = {in: "amd64", in: "arm64"}
  ];

  string description = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件描述信息"}
  ];

  string destPath = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件存放路径"}
  ];

  string fileIdentifier = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件唯一标识"}
  ];

  string filename = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件原始路径"}
  ];

  string hostOsType = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统:\n[ centos openEuler kylin]"}
  ];

  string md5 = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件md5值"}
  ];

  bool override = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否覆盖记录"}
  ];

  string region = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件的region信息"}
  ];

  string serviceCode = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品service code"}
  ];

  string storageLocation = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储位置:\n [s3 http]"}
  ];

  string version = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"}
  ];

}
message RpmTaskReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["serviceCode", "filename", "appName", "fileIdentifier", "repo", "md5", "version", "region", "architecture", "hostOsType"]}};

  string appName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件对应的app名称"}
  ];

  string architecture = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ amd64 arm64 ]"},
    (validate.rules).string = {in: "amd64", in: "arm64"}
  ];

  string description = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件描述信息"}
  ];

  string fileIdentifier = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件原始路径"}
  ];

  string filename = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件存放路径"}
  ];

  string hostOsType = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"}
  ];

  string md5 = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件md5值"}
  ];

  bool override = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否覆盖记录"}
  ];

  string region = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "RPM包应用名称"}
  ];

  string repo = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "仓库名称\n- 文件类型 [ rpm ] 必填可选: [ release iaas-release oe-release ... ]"}
  ];

  string serviceCode = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品service code"}
  ];

  string version = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"}
  ];

}
message SkywingPackageTaskReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["serviceCode", "filename", "moduleName", "packageVersion", "appName", "md5", "architecture", "version", "region", "hostOsType"]}};

  string appName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼应用名称"}
  ];

  string architecture = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ amd64 arm64 ]"},
    (validate.rules).string = {in: "amd64", in: "arm64"}
  ];

  string description = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件描述信息"}
  ];

  string filename = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件存放路径"}
  ];

  string hostOsType = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"}
  ];

  string md5 = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件md5值"}
  ];

  string moduleName = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼模块名称"}
  ];

  bool override = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否覆盖记录"}
  ];

  string packageVersion = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼模块名称"}
  ];

  string region = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack Region信息"}
  ];

  string serviceCode = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品service code"}
  ];

  string version = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"}
  ];
}
message DockerImageTaskReq{
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["serviceCode", "filename", "fileIdentifier", "registry", "tag", "type", "appName", "md5", "architecture", "version", "region", "hostOsType"]}};

  string appName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用名称"}
  ];

  string architecture = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ amd64 arm64 ]"},
    (validate.rules).string = {in: "amd64", in: "arm64"}
  ];

  string description = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件描述信息"}
  ];

  string fileIdentifier = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件原始路径"}
  ];

  string filename = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件存放路径"}
  ];

  string hostOsType = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"}
  ];

  string md5 = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件md5值"}
  ];

  bool override = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否覆盖记录"}
  ];

  string region = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack Region信息"}
  ];

  string registry = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件名称"}
  ];

  string serviceCode = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品service code"}
  ];

  string tag = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼模块名称"}
  ];

  string type = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "上传文件md5值"}
  ];

  string version = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"}
  ];
}

message DescribeCommonReply {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["code", "message"]}};

  int64 code = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "code"}
  ];

  string message = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "message"}
  ];
}

message DescribeTaskReply {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["code", "message", "data"]}};

  int64 code = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "code"}
  ];

  taskInfo data = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "data"}
  ];
  string message = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "message"}
  ];
}

message taskInfo {
  string taskID = 1;
}
message   DescribeTaskReq{
  string taskID = 1;
}
message PushTaskInfo {
  string createTime = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "create time"}
  ];

  string message = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件描述信息"}
  ];

  int64 retry = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务重试次数"}
  ];

  string state = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务状态"}
  ];

  string updateTime = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "update time"}
  ];
}
message DescribeCommonFileTaskReply {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["code", "message", "data"]}};

  int64 code = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "code"}
  ];

  PushTaskInfo data = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "data"}
  ];

  string message = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "message"}
  ];
}
message DescribesArtifactReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["version", "architecture"]}};

  string serviceCode = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "Service code"}
  ];

  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"}
  ];

  string architecture = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[x86_64 arm]"},
    (validate.rules).string = {in: "x86_64", in: "arm"}
  ];

  string hostOSType = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统:\n[ centos openEuler kylin]"},
    (validate.rules).string = {ignore_empty: true, in: "centos", in: "openEuler", in: "kylin"}
  ];

  string appName = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用名称"}
  ];
}
message DescribesArtifactReply{
  repeated AppArtifact regularFile = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "普通文件相关信息"}
  ];

  repeated AppArtifact rpm = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "RPM包相关信息"}
  ];

  repeated AppArtifact skywingImage = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼镜像部署相关信息"}
  ];

  repeated AppArtifact skywingPackage = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云翼包部署相关信息"}
  ];

  repeated AppArtifact xingyunImage = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "行云包部署相关信息"}
  ];

  repeated AppArtifact xingyunPackage = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "行云镜像部署相关信息"}
  ];
}
message AppArtifact {
  string appName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用名称"}
  ];

  string appServiceCode = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "app service code信息"}
  ];

  string appVersion = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用版本信息"}
  ];

  string fileIdentifier = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用版本信息"}
  ];

  string hostOsType = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"}
  ];
}
message CreateArtifactPackageReq{
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {required: ["destHostIp", "destPath", "taskType", "packageVersion", "architecture", "hostOsTypes"]}
  };

  string architecture = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "部署包使用CPU架构 : \n[amd64, arm64]", enum: ["amd64", "arm64"]
    }
  ];

  string baseVersion = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "用于生成增量部署包的基准版本"}
  ];

  string destHostIp = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "目标服务器IP地址"}
  ];

  string destPath = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包存放路径"}
  ];

  int64 destSshPort = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "打包服务器ssh端口"}
  ];

  repeated string hostOsTypes = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "操作系统类型(支持多选): \n[centos, openeuler22, kylinv10]"
    },
    (validate.rules).repeated.items.string = {in: "centos", in: "openeuler22", in: "kylinv10"}
  ];

  string packageVersion = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包版本"}
  ];

  string packageVersionType = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包版本类型"}
  ];

  int64 snapshotGeneratedTimeStamp = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "快照生成时间戳,秒级别"}
  ];

  string taskType = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "部署包任务类型: \n[increment, full]", enum: ["increment", "full"]
    }
  ];

  string jdstackReleaseS3 = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "版本ADL包存储位置S3,如果不为空,则运行相应命令下载到指定位置"}
  ];

  bool clearArtifactDirBeforePackage = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否在触发打包前清空制品目录,目前仅清空当前架构目录和公共yum目录"}
  ];

  repeated string specResources = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "指定产品(resource)出包预留字段"}
  ];
}
message DescribeArtifactPackageReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {required: ["packageVersion", "architecture", "hostOsType"]}
  };
  string packageVersion = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包版本"}
  ];

  string architecture = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "CPU架构"}
  ];

  string hostOsType = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"}
  ];
}

message DescribeArtifactPackageReply {
  string status = 1[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务状态"}
  ];
  string errorMsg = 2[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "错误信息"}
  ];
  string log = 3[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "日志"}
  ];
}
message CreateMetadataPackageReq{
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {required: ["destHostIp", "destPath", "taskType", "packageVersion", "architecture", "hostOsType"]}
  };

  string baseVersion = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "用于生成增量部署包的基准版本"}
  ];

  string destHostIp = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "目标服务器IP地址"}
  ];

  string destPath = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包存放路径"}
  ];

  int64 destSshPort = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "打包服务器ssh端口"}
  ];

  string packageVersion = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包版本"}
  ];

  string taskType = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "部署包任务类型", enum: ["increment", "full"]
    }
  ];
}
message PackageTaskInfo {
  string destHostIp = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "目标服务器IP地址"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string destPath = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包存放路径"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string packageVersion = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署包版本"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  int64 destSshPort = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "打包服务器ssh端口"}
  ];

  string state = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务状态"},
    (validate.rules).string = {min_len: 0, max_len: 128}
  ];

  string message = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务描述信息"},
    (validate.rules).string = {min_len: 0, max_len: 128}
  ];

  string architecture = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string hostOsType = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string createTime = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "create time"}
  ];

  string updateTime = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "update time"}
  ];
}
message DescribeMetadataPackageReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
    json_schema: {
      required: ["packageVersion"]
    }
  };

  string packageVersion = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "部署包版本"
    }
  ];
  string createTime = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "create time"}
  ];
}
message DescribeSCFileTaskReply {
  int64 code = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "code"}
  ];

  DescribeSCFilesResponseData data = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "data"}
  ];

  string message = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "message"},
    (validate.rules).string = {min_len: 0, max_len: 1024}
  ];
}

message DescribeSCFilesResponseData {
  string appName = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品service code"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string architecture = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件的架构信息"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string createTime = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "create time"}
  ];

  string hostOsType = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string message = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务描述信息"},
    (validate.rules).string = {min_len: 0, max_len: 128}
  ];

  string region = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件的region信息"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string serviceCode = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品service code"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];

  string state = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务状态"},
    (validate.rules).string = {min_len: 0, max_len: 128}
  ];

  string updateTime = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "update time"}
  ];

  string version = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_len: 0, max_len: 64}
  ];
}
message CreateSCFilesTaskReq {
  string serviceCode = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品service code"},
    (validate.rules).string = {min_len: 1, max_len: 64}
  ];

  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_len: 1, max_len: 64}
  ];

  string region = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "环境信息"},
    (validate.rules).string = {min_len: 1, max_len: 64}
  ];

  string architecture = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件的架构信息"},
    (validate.rules).string = {min_len: 1, max_len: 64}
  ];

  string hostOsType = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统类型"},
    (validate.rules).string = {ignore_empty: true, in: ["centos", "openEuler", "kylin"]}
  ];

  string appName = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用名称"}
  ];
}

message AppFilesTaskReq {
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_len: 1, max_len: 64}
  ];
  string serviceCode = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "serviceCode"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string proCode = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品Code"},
    (validate.rules).string = {min_bytes: 1}
  ];
  string appCode = 4[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用Code"}
  ];

  string appVersionID = 5[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用版本id"}
  ];

  string deployMode = 6[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署模式:\n[ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]"},
    (validate.rules).string = {in: "skywing_container", in: "skywing_package", in: "mate_container", in: "shell", in: "helm", in: "rpm", in: "mate_bin", in: "mate_v2", in: "iaas_image", in: "xingyun_container", in: "xingyun_package"}
  ];

  string envName = 7[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "环境名称"}
  ];

  string region = 8[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "环境region"}
  ];

  repeated pushFileInfo artifactInfos = 9[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "制品信息"}
  ];
}

message pushFileInfo {
  string fileType = 1[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件类型:\n[ image out_image os_file rpm package helm ]"},
    (validate.rules).string = {in: "image", in: "out_image", in: "rpm", in: "os_file", in: "package", in: "helm"}
  ];
  string fileName = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件名称"}
  ];
  string md5 = 3[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "md5"}
  ];
  string arch = 4[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ amd64 arm64 ]"},
    (validate.rules).string = {in: "amd64", in: "arm64"}
  ];
  string os = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统"},
    (validate.rules).string = {in: ["centos", "openeuler22", "kylinv10"]}
  ];
  string fileStorageMode = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储位置类型\n [ http minio ]"}
  ];
  string fileBucket = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件存储bucket"}
  ];
  string rpmRepo = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "rpm仓库名称\n [ release iaas-release oe-release]"}
  ];
  string imageRepo = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像repo"}
  ];
  string imageTag = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "镜像tag"}
  ];
  string pkgModelName = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包模块名称"}
  ];
  string pkgVersion = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "包版本"}
  ];
  string identity = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件标识"}
  ];
  string version = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件版本"}
  ];
}
message DescribeAppFileTaskReply {
  string status = 1[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "任务状态"}
  ];
  string errorMsg = 2[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "错误信息"}
  ];
  string log = 3[
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "日志"}
  ];
}

message CreateArtifactReq {
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_len: 1, max_len: 64}
  ];

  string appCode = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "应用Code"}
  ];

  string arch = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "架构:\n[ x86_64 arm ]"},
    (validate.rules).string = {in: "x86_64", in: "arm"}
  ];

  string hostOsType = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "操作系统发行版：HostOsTypeCentos、HostOsTypeOpenEuler"},
    (validate.rules).string = {in: ["centos", "openEuler", "kylin"]}
  ];

  string fileIdentifier = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像标识"}
  ];

  string directorySpec = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "目录命名规范：DirectorySpecArchFileTypeServiceCode、DirectorySpecArchSerializeGoods"},
    (validate.rules).string = {in: ["DirectorySpecArchFileTypeServiceCode", "DirectorySpecArchSerializeGoods"]}
  ];

  string filename = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像名称"}
  ];

  string fileHash = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件校验码"}
  ];

  int64 fileSize = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件大小/字节"}
  ];

  string imageTags = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件或镜像版本"}
  ];

  string fileVersion = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "文件版本"}
  ];

  string param = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "参数"}
  ];
}

// 新增响应消息
message DeploymentServerInfoReply {
  string ip = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署服务器IP地址"}];
  // 为未来扩展预留字段
  string path = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "部署路径（预留）"}];
}
