syntax = "proto3";

package jdstack.zeus.v1.version;

option go_package = "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/version/v1;pbVersion";

import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/empty.proto";
import "winapi/winapi.proto";
import "tagger/tagger.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  host: "localhost:35711"
  base_path: "/v1"
  schemes: [HTTP]
  info: {
    title: "Version_JDStack版本信息"
    version: "v1"
  }
};

service Version {
  rpc Create (CreateReq) returns (DescribeReply) {
    option (google.api.http) = {post: "/version", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建JDStack版本信息"
    };
  }
  rpc Modify (ModifyReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {put: "/version/{id}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "修改JDStack版本信息"
    };
  }
  rpc Describe (DescribeReq) returns (DescribeReply) {
    option (google.api.http) = {get: "/version/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取JDStack版本信息"
    };
  }
  rpc Describes (DescribesReq) returns (DescribesReply) {
    option (google.api.http) = {get: "/version"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取JDStack版本列表"
    };
  }

  rpc CreateCvessel (CreateCvesselReq) returns (DescribeCvesselReply) {
    option (google.api.http) = {post: "/CvesselVersion", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建云舰版本信息"
    };
  }
  rpc ModifyCvessel (ModifyCvesselReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {put: "/CvesselVersion/{id}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "修改云舰版本信息"
    };
  }
  rpc DescribeCvessel (DescribeCvesselReq) returns (DescribeCvesselReply) {
    option (google.api.http) = {get: "/cvesselVersion/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取云舰版本信息"
    };
  }
  rpc DescribesCvessel (DescribesCvesselReq) returns (DescribesCvesselReply) {
    option (google.api.http) = {get: "/cvesselVersion"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取云舰版本列表"
    };
  }
  // 直接将快照导入zeus使用的接口 HACK: 这里有演进历程，当前的实际逻辑和命名可能不match，以实际逻辑为主
  rpc CreateCvesselMetadata (CreateCvesselMetadataReq) returns (CreateCvesselMetadataReply) {
    option (google.api.http) = {post: "/cvesselVersion/{version}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "接收天基云舰版本元数据包并导入zeus库"
    };
  }
  rpc ModifyCvesselMetadata (ModifyCvesselMetadataReq) returns (ModifyCvesselMetadataReply) {
    option (google.api.http) = {post: "/cvesselVersion/{version}", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "创建/更新zeus库内天基云舰版本元数据,只保留一份,不存在则新建"
    };
  }
  rpc DeleteCvesselMetadata (DeleteCvesselMetadataReq) returns (DeleteCvesselMetadataReply) {
    option (google.api.http) = {delete: "/cvesselVersion/{version}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "删除zeus库内天基云舰版本元数据"
    };
  }
  rpc DescribeReleaseConfigFile (DescribeReleaseConfigFileReq) returns (DescribeReleaseConfigFilesReply) {
    option (google.api.http) = {get: "/releaseVersion/{version}/fileName/{fileName}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取zeus库内封版版本内制定配置文件,由于相对路径不同,相同名称可能是多份"
    };
  }
  rpc ModifyDependencies (ModifyDependenciesReq) returns (ModifyDependenciesReply) {
    option (google.api.http) = {post: "/version/{version}/modifyDependencies", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "全量更新版本内产品间部署依赖信息,仅限云舰版本且仅处理主集群,最终数据会注入goods_chain"
    };
  }

  rpc CreateSqlExportToS3 (CreateSqlExportToS3Req) returns (CreateSqlExportToS3Reply) {
    option (google.api.http) = {post: "/version/{version}/exportSql", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "导出指定版本的SQL文件并上传至S3"
    };
  }

  rpc DescribeDeployVersionInfo (DescribeDeployVersionInfoReq) returns (DescribeDeployVersionInfoReply) {
    option (google.api.http) = {get: "/describeDeployVersionInfo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      description: "获取离线交付场景下,想要交付的版本信息,前提是离线数据库中仅包含单个版本的数据,或包含前序版本的数据"
    };
  }
}

message CreateReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["version", "prevPlatform"]}};
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z.]+$"},
    (tagger.tags) = 'excel:"JDStack版本" required:"true"'
  ];
  string description = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"},
    (tagger.tags) = 'excel:"描述"'
  ];
  string prevPlatform = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "前序版本"},
    (validate.rules).string = {min_bytes: 1},
    (tagger.tags) = 'excel:"前序版本" required:"true"'
  ];
}

message ModifyReq {
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本ID\n- 为0时按顺序查询字段：version"},
    (validate.rules).int32 = {ignore_empty: true, gte: 0}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本,只读"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z.]+$"}
  ];
  repeated string clearFields = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "清空字段:\ndescription - 描述"},
    (validate.rules).repeated.items.string = {in: "description"}
  ];
  string description = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  string locked = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否锁定:\n[ no yes ]"},
    (validate.rules).string = {ignore_empty: true, in: "no", in: "yes"}
  ];
}

message DescribeReq {
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本ID\n- 为0时按顺序查询字段：version"},
    (validate.rules).int32 = {ignore_empty: true, gte: 0}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z.]+$"},
    (tagger.tags) = 'excel:"JDStack版本" required:"true"'
  ];
}
message DescribeReply {
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本ID,特殊情况下使用"}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  string description = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  string prevPlatform = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "前序版本,filter[With:Prev]"}
  ];
  string nextPlatform = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "后序版本,filter[With:Next]"}
  ];
  string locked = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否锁定:\n[ no yes ]"}
  ];
  string cloudType = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "版本类型:\n[ jdstack cvessel ]"}
  ];
  string relatedPlatform = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联版本,用于描述JDStack关联的云舰版本号"}
  ];
}

message DescribesReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesReply {
  repeated DescribeReply list = 1;
  int64 totalCount = 2;
}

message CreateCvesselReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["name", "version", "prevPlatform"]}};
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本ID,仅供导入"},
    (google.api.field_visibility).restriction = "INTERNAL",
    (tagger.tags) = 'excel:"ID"'
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本"},
    (validate.rules).string = {min_bytes: 1},
    (tagger.tags) = 'excel:"云舰版本" required:"true"'
  ];
  string jdstackVersion = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "JDStack版本"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z.]+$"},
    (tagger.tags) = 'excel:"JDStack版本" required:"true"'
  ];
  string description = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"},
    (tagger.tags) = 'excel:"描述"'
  ];
  string preVersion = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "前序版本"},
    (validate.rules).string = {ignore_empty: true},
    (tagger.tags) = 'excel:"前序版本" required:"true"'
  ];
}

message ModifyCvesselReq {
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本ID\n- 为0时按顺序查询字段：version"},
    (validate.rules).int32 = {ignore_empty: true, gte: 0}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本,只读"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z.]+$"}
  ];
  repeated string clearFields = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "清空字段:\ndescription - 描述"},
    (validate.rules).repeated.items.string = {in: "description"}
  ];
  string description = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
}

message DescribeCvesselReq {
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本ID\n- 为0时按顺序查询字段：version"},
    (validate.rules).int32 = {ignore_empty: true, gte: 0}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z.]+$"},
    (tagger.tags) = 'excel:"云舰版本" required:"true"'
  ];
}
message DescribeCvesselReply {
  int32 id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本ID,特殊情况下使用"}
  ];
  string version = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云舰版本"}
  ];
  string createdAt = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "创建时间,UTC格式(毫秒级24位)"}
  ];
  DescribeReply jdstackVersion = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "关联的jdstack版本,filter[With:RelatedRelease]"}
  ];
  string description = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "描述"}
  ];
  string preVersion = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "前序版本,filter[With:Prev]"}
  ];
  repeated string nextVersions = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "后序版本,filter[With:Next]"}
  ];
}
message DescribesCvesselReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["pageNumber", "pageSize"]}};
  int64 pageNumber = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "页码:\n-1 - 全部\n0 - 第1页\n1 - 第2页\n- 以此类推...", default: "0"},
    (validate.rules).int64 = {gte: -1, lte: 16777216}
  ];
  int64 pageSize = 2 [(validate.rules).int64 = {gte: 1, lte: 16777216}];
  repeated wingo.api.Filter filters = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持筛选，筛选扩展:\nWith:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)\nWith:{模块名}.{字段名} - 过滤并返回关联表\nHas:{模块名}.{字段名} - 子查询(等值连接)"}
  ];
  repeated wingo.api.Sort sorts = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回字段皆支持排序，排序扩展:\nWith:{模块名}.[ Min Max Count Sum ]:{字段名} - 按关联表字段排序"}
  ];
}
message DescribesCvesselReply {
  repeated DescribeCvesselReply list = 1;
  int64 totalCount = 2;
}

message DescribeDeployVersionInfoReq {
  string cloudType = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "云类型: jdstack | cvessel"},
    (validate.rules).string = {in: "jdstack", in: "cvessel"}
  ];
}

message DescribeDeployVersionInfoReply {
  DescribeReply deployVersion = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "当前数据库中包含的即将交付的版本"}
  ];
  DescribeReply deployVersionPrev = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "当前数据库中包含的即将交付的版本的前序版本,如果不存在则为空"}
  ];
}

message CreateCvesselMetadataReq {
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:封版版本"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string versionType = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:封版版本类型"},
    (validate.rules).string = {ignore_empty: true}
  ];
  string snapshotVersion = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:云舰版本封版快照号"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string preVersion = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "预留字段,前序版本"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string jdstackVersion = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "此封版快照所关联的jdstack版本号,版本统一后如果是jdstack封版类型调用,此字段无效"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  bytes data = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "封版快照内容字节流"}
  ];
  bool forceOverride = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "是否强制覆盖版本数据,请谨慎操作",
      default: "true"
    }
  ];
  string snapshotGeneratedTime = 8 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "快照生成时间,UTC格式(毫秒级24位)"},
    (validate.rules).string = {ignore_empty: true, pattern: "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?Z$"}
  ];
}

message ModifyCvesselMetadataReq {
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:封版版本"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string versionType = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:封版版本类型"},
    (validate.rules).string = {ignore_empty: true}
  ];
  string snapshotVersion = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:云舰版本封版快照号"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string preVersion = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "预留字段,前序版本"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string jdstackVersion = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "此封版快照所关联的jdstack版本号,版本统一后如果是jdstack封版类型调用,此字段无效"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  bytes data = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "封版快照内容字节流"}
  ];
}

message DeleteCvesselMetadataReq {
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:云舰版本号"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string versionType = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:封版版本类型"},
    (validate.rules).string = {ignore_empty: true}
  ];
  string jdstackVersion = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "此封版快照所关联的jdstack版本号,版本统一后如果是jdstack封版类型调用,此字段无效"},
    (validate.rules).string = {ignore_empty: true, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
}

message CreateCvesselMetadataReply {
  string id = 1;
  string status = 2;
}

message ModifyCvesselMetadataReply {
  string id = 1;
  string status = 2;
}

message DeleteCvesselMetadataReply {
  string id = 1;
  string status = 2;
}

message DescribeReleaseConfigFileReq {
  string version = 1;
  string cloudType = 2;
  string fileName = 3;
}

message DescribeReleaseConfigFileReply {
  string fileName = 1;
  string relativePath = 2;
  string content = 3;
}

message DescribeReleaseConfigFilesReply {
  repeated DescribeReleaseConfigFileReply list = 1;
  int64 totalCount = 2;
}

// ModifyDependencies related messages
message ModifyDependenciesReq {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {json_schema: {required: ["version"]}};
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "封版版本号,仅限更改"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  repeated ProductInfo products = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品列表"}
  ];
}

message ProductInfo {
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "产品唯一标识"}
  ];
  repeated string dependencies = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "依赖列表"}
  ];
  bool required = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否必需"}
  ];
}

message ModifyDependenciesReply {
  string status = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "更新状态"}
  ];
  string message = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回消息"}
  ];
}

// CreateSqlExportToS3 相关消息
message CreateSqlExportToS3Req {
  string version = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "Zeus版本号"},
    (validate.rules).string = {min_bytes: 1, pattern: "^[0-9a-zA-Z._-]+$"}
  ];
  string versionType = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "命名方式:封版版本类型"},
    (validate.rules).string = {ignore_empty: true}
  ];
  bool includePrevVersion = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "是否包含前序版本数据", default: "false"}
  ];
}

message CreateSqlExportToS3Reply {
  string s3Bucket = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "导出SQL文件在S3上的Bucket"}
  ];
  string s3ObjectKey = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "导出SQL文件在S3上的ObjectKey"}
  ];
  string status = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "导出状态"}
  ];
  string message = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {description: "返回消息"}
  ];
}
