// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: version.proto

package pbVersion

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateReqMultiError, or nil
// if none found.
func (m *CreateReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(m.GetVersion()) < 1 {
		err := CreateReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := CreateReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	if len(m.GetPrevPlatform()) < 1 {
		err := CreateReqValidationError{
			field:  "PrevPlatform",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateReqMultiError(errors)
	}

	return nil
}

// CreateReqMultiError is an error wrapping multiple validation errors returned
// by CreateReq.ValidateAll() if the designated constraints aren't met.
type CreateReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateReqMultiError) AllErrors() []error { return m }

// CreateReqValidationError is the validation error returned by
// CreateReq.Validate if the designated constraints aren't met.
type CreateReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateReqValidationError) ErrorName() string { return "CreateReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateReqValidationError{}

var _CreateReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

// Validate checks the field values on ModifyReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModifyReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModifyReqMultiError, or nil
// if none found.
func (m *ModifyReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() != 0 {

		if m.GetId() < 0 {
			err := ModifyReqValidationError{
				field:  "Id",
				reason: "value must be greater than or equal to 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVersion() != "" {

		if !_ModifyReq_Version_Pattern.MatchString(m.GetVersion()) {
			err := ModifyReqValidationError{
				field:  "Version",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetClearFields() {
		_, _ = idx, item

		if _, ok := _ModifyReq_ClearFields_InLookup[item]; !ok {
			err := ModifyReqValidationError{
				field:  fmt.Sprintf("ClearFields[%v]", idx),
				reason: "value must be in list [description]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	if m.GetLocked() != "" {

		if _, ok := _ModifyReq_Locked_InLookup[m.GetLocked()]; !ok {
			err := ModifyReqValidationError{
				field:  "Locked",
				reason: "value must be in list [no yes]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ModifyReqMultiError(errors)
	}

	return nil
}

// ModifyReqMultiError is an error wrapping multiple validation errors returned
// by ModifyReq.ValidateAll() if the designated constraints aren't met.
type ModifyReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyReqMultiError) AllErrors() []error { return m }

// ModifyReqValidationError is the validation error returned by
// ModifyReq.Validate if the designated constraints aren't met.
type ModifyReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyReqValidationError) ErrorName() string { return "ModifyReqValidationError" }

// Error satisfies the builtin error interface
func (e ModifyReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyReqValidationError{}

var _ModifyReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

var _ModifyReq_ClearFields_InLookup = map[string]struct{}{
	"description": {},
}

var _ModifyReq_Locked_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

// Validate checks the field values on DescribeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribeReqMultiError, or
// nil if none found.
func (m *DescribeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() != 0 {

		if m.GetId() < 0 {
			err := DescribeReqValidationError{
				field:  "Id",
				reason: "value must be greater than or equal to 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVersion() != "" {

		if !_DescribeReq_Version_Pattern.MatchString(m.GetVersion()) {
			err := DescribeReqValidationError{
				field:  "Version",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DescribeReqMultiError(errors)
	}

	return nil
}

// DescribeReqMultiError is an error wrapping multiple validation errors
// returned by DescribeReq.ValidateAll() if the designated constraints aren't met.
type DescribeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeReqMultiError) AllErrors() []error { return m }

// DescribeReqValidationError is the validation error returned by
// DescribeReq.Validate if the designated constraints aren't met.
type DescribeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeReqValidationError) ErrorName() string { return "DescribeReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeReqValidationError{}

var _DescribeReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

// Validate checks the field values on DescribeReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribeReplyMultiError, or
// nil if none found.
func (m *DescribeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Version

	// no validation rules for CreatedAt

	// no validation rules for Description

	// no validation rules for PrevPlatform

	// no validation rules for NextPlatform

	// no validation rules for Locked

	// no validation rules for CloudType

	// no validation rules for RelatedPlatform

	if len(errors) > 0 {
		return DescribeReplyMultiError(errors)
	}

	return nil
}

// DescribeReplyMultiError is an error wrapping multiple validation errors
// returned by DescribeReply.ValidateAll() if the designated constraints
// aren't met.
type DescribeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeReplyMultiError) AllErrors() []error { return m }

// DescribeReplyValidationError is the validation error returned by
// DescribeReply.Validate if the designated constraints aren't met.
type DescribeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeReplyValidationError) ErrorName() string { return "DescribeReplyValidationError" }

// Error satisfies the builtin error interface
func (e DescribeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeReplyValidationError{}

// Validate checks the field values on DescribesReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribesReqMultiError, or
// nil if none found.
func (m *DescribesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesReqMultiError(errors)
	}

	return nil
}

// DescribesReqMultiError is an error wrapping multiple validation errors
// returned by DescribesReq.ValidateAll() if the designated constraints aren't met.
type DescribesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesReqMultiError) AllErrors() []error { return m }

// DescribesReqValidationError is the validation error returned by
// DescribesReq.Validate if the designated constraints aren't met.
type DescribesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesReqValidationError) ErrorName() string { return "DescribesReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesReqValidationError{}

// Validate checks the field values on DescribesReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescribesReplyMultiError,
// or nil if none found.
func (m *DescribesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesReplyMultiError(errors)
	}

	return nil
}

// DescribesReplyMultiError is an error wrapping multiple validation errors
// returned by DescribesReply.ValidateAll() if the designated constraints
// aren't met.
type DescribesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesReplyMultiError) AllErrors() []error { return m }

// DescribesReplyValidationError is the validation error returned by
// DescribesReply.Validate if the designated constraints aren't met.
type DescribesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesReplyValidationError) ErrorName() string { return "DescribesReplyValidationError" }

// Error satisfies the builtin error interface
func (e DescribesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesReplyValidationError{}

// Validate checks the field values on CreateCvesselReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateCvesselReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCvesselReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCvesselReqMultiError, or nil if none found.
func (m *CreateCvesselReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCvesselReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(m.GetVersion()) < 1 {
		err := CreateCvesselReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetJdstackVersion() != "" {

		if !_CreateCvesselReq_JdstackVersion_Pattern.MatchString(m.GetJdstackVersion()) {
			err := CreateCvesselReqValidationError{
				field:  "JdstackVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	if m.GetPreVersion() != "" {

	}

	if len(errors) > 0 {
		return CreateCvesselReqMultiError(errors)
	}

	return nil
}

// CreateCvesselReqMultiError is an error wrapping multiple validation errors
// returned by CreateCvesselReq.ValidateAll() if the designated constraints
// aren't met.
type CreateCvesselReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCvesselReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCvesselReqMultiError) AllErrors() []error { return m }

// CreateCvesselReqValidationError is the validation error returned by
// CreateCvesselReq.Validate if the designated constraints aren't met.
type CreateCvesselReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCvesselReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCvesselReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCvesselReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCvesselReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCvesselReqValidationError) ErrorName() string { return "CreateCvesselReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateCvesselReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCvesselReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCvesselReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCvesselReqValidationError{}

var _CreateCvesselReq_JdstackVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

// Validate checks the field values on ModifyCvesselReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ModifyCvesselReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyCvesselReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyCvesselReqMultiError, or nil if none found.
func (m *ModifyCvesselReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyCvesselReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() != 0 {

		if m.GetId() < 0 {
			err := ModifyCvesselReqValidationError{
				field:  "Id",
				reason: "value must be greater than or equal to 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVersion() != "" {

		if !_ModifyCvesselReq_Version_Pattern.MatchString(m.GetVersion()) {
			err := ModifyCvesselReqValidationError{
				field:  "Version",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetClearFields() {
		_, _ = idx, item

		if _, ok := _ModifyCvesselReq_ClearFields_InLookup[item]; !ok {
			err := ModifyCvesselReqValidationError{
				field:  fmt.Sprintf("ClearFields[%v]", idx),
				reason: "value must be in list [description]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	if len(errors) > 0 {
		return ModifyCvesselReqMultiError(errors)
	}

	return nil
}

// ModifyCvesselReqMultiError is an error wrapping multiple validation errors
// returned by ModifyCvesselReq.ValidateAll() if the designated constraints
// aren't met.
type ModifyCvesselReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyCvesselReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyCvesselReqMultiError) AllErrors() []error { return m }

// ModifyCvesselReqValidationError is the validation error returned by
// ModifyCvesselReq.Validate if the designated constraints aren't met.
type ModifyCvesselReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyCvesselReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyCvesselReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyCvesselReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyCvesselReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyCvesselReqValidationError) ErrorName() string { return "ModifyCvesselReqValidationError" }

// Error satisfies the builtin error interface
func (e ModifyCvesselReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyCvesselReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyCvesselReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyCvesselReqValidationError{}

var _ModifyCvesselReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

var _ModifyCvesselReq_ClearFields_InLookup = map[string]struct{}{
	"description": {},
}

// Validate checks the field values on DescribeCvesselReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeCvesselReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeCvesselReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeCvesselReqMultiError, or nil if none found.
func (m *DescribeCvesselReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeCvesselReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() != 0 {

		if m.GetId() < 0 {
			err := DescribeCvesselReqValidationError{
				field:  "Id",
				reason: "value must be greater than or equal to 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVersion() != "" {

		if !_DescribeCvesselReq_Version_Pattern.MatchString(m.GetVersion()) {
			err := DescribeCvesselReqValidationError{
				field:  "Version",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DescribeCvesselReqMultiError(errors)
	}

	return nil
}

// DescribeCvesselReqMultiError is an error wrapping multiple validation errors
// returned by DescribeCvesselReq.ValidateAll() if the designated constraints
// aren't met.
type DescribeCvesselReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeCvesselReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeCvesselReqMultiError) AllErrors() []error { return m }

// DescribeCvesselReqValidationError is the validation error returned by
// DescribeCvesselReq.Validate if the designated constraints aren't met.
type DescribeCvesselReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeCvesselReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeCvesselReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeCvesselReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeCvesselReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeCvesselReqValidationError) ErrorName() string {
	return "DescribeCvesselReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeCvesselReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeCvesselReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeCvesselReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeCvesselReqValidationError{}

var _DescribeCvesselReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

// Validate checks the field values on DescribeCvesselReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeCvesselReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeCvesselReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeCvesselReplyMultiError, or nil if none found.
func (m *DescribeCvesselReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeCvesselReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Version

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetJdstackVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeCvesselReplyValidationError{
					field:  "JdstackVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeCvesselReplyValidationError{
					field:  "JdstackVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJdstackVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeCvesselReplyValidationError{
				field:  "JdstackVersion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Description

	// no validation rules for PreVersion

	if len(errors) > 0 {
		return DescribeCvesselReplyMultiError(errors)
	}

	return nil
}

// DescribeCvesselReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeCvesselReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeCvesselReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeCvesselReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeCvesselReplyMultiError) AllErrors() []error { return m }

// DescribeCvesselReplyValidationError is the validation error returned by
// DescribeCvesselReply.Validate if the designated constraints aren't met.
type DescribeCvesselReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeCvesselReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeCvesselReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeCvesselReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeCvesselReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeCvesselReplyValidationError) ErrorName() string {
	return "DescribeCvesselReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeCvesselReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeCvesselReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeCvesselReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeCvesselReplyValidationError{}

// Validate checks the field values on DescribesCvesselReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesCvesselReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesCvesselReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesCvesselReqMultiError, or nil if none found.
func (m *DescribesCvesselReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesCvesselReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPageNumber(); val < -1 || val > 16777216 {
		err := DescribesCvesselReqValidationError{
			field:  "PageNumber",
			reason: "value must be inside range [-1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 16777216 {
		err := DescribesCvesselReqValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 16777216]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesCvesselReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesCvesselReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesCvesselReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesCvesselReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesCvesselReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesCvesselReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesCvesselReqMultiError(errors)
	}

	return nil
}

// DescribesCvesselReqMultiError is an error wrapping multiple validation
// errors returned by DescribesCvesselReq.ValidateAll() if the designated
// constraints aren't met.
type DescribesCvesselReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesCvesselReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesCvesselReqMultiError) AllErrors() []error { return m }

// DescribesCvesselReqValidationError is the validation error returned by
// DescribesCvesselReq.Validate if the designated constraints aren't met.
type DescribesCvesselReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesCvesselReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesCvesselReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesCvesselReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesCvesselReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesCvesselReqValidationError) ErrorName() string {
	return "DescribesCvesselReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesCvesselReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesCvesselReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesCvesselReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesCvesselReqValidationError{}

// Validate checks the field values on DescribesCvesselReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesCvesselReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesCvesselReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesCvesselReplyMultiError, or nil if none found.
func (m *DescribesCvesselReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesCvesselReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesCvesselReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesCvesselReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesCvesselReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribesCvesselReplyMultiError(errors)
	}

	return nil
}

// DescribesCvesselReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesCvesselReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesCvesselReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesCvesselReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesCvesselReplyMultiError) AllErrors() []error { return m }

// DescribesCvesselReplyValidationError is the validation error returned by
// DescribesCvesselReply.Validate if the designated constraints aren't met.
type DescribesCvesselReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesCvesselReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesCvesselReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesCvesselReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesCvesselReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesCvesselReplyValidationError) ErrorName() string {
	return "DescribesCvesselReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesCvesselReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesCvesselReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesCvesselReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesCvesselReplyValidationError{}

// Validate checks the field values on DescribeDeployVersionInfoReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeDeployVersionInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeDeployVersionInfoReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeDeployVersionInfoReqMultiError, or nil if none found.
func (m *DescribeDeployVersionInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeDeployVersionInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _DescribeDeployVersionInfoReq_CloudType_InLookup[m.GetCloudType()]; !ok {
		err := DescribeDeployVersionInfoReqValidationError{
			field:  "CloudType",
			reason: "value must be in list [jdstack cvessel]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DescribeDeployVersionInfoReqMultiError(errors)
	}

	return nil
}

// DescribeDeployVersionInfoReqMultiError is an error wrapping multiple
// validation errors returned by DescribeDeployVersionInfoReq.ValidateAll() if
// the designated constraints aren't met.
type DescribeDeployVersionInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeDeployVersionInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeDeployVersionInfoReqMultiError) AllErrors() []error { return m }

// DescribeDeployVersionInfoReqValidationError is the validation error returned
// by DescribeDeployVersionInfoReq.Validate if the designated constraints
// aren't met.
type DescribeDeployVersionInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeDeployVersionInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeDeployVersionInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeDeployVersionInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeDeployVersionInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeDeployVersionInfoReqValidationError) ErrorName() string {
	return "DescribeDeployVersionInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeDeployVersionInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeDeployVersionInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeDeployVersionInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeDeployVersionInfoReqValidationError{}

var _DescribeDeployVersionInfoReq_CloudType_InLookup = map[string]struct{}{
	"jdstack": {},
	"cvessel": {},
}

// Validate checks the field values on DescribeDeployVersionInfoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeDeployVersionInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeDeployVersionInfoReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribeDeployVersionInfoReplyMultiError, or nil if none found.
func (m *DescribeDeployVersionInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeDeployVersionInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDeployVersion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeDeployVersionInfoReplyValidationError{
					field:  "DeployVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeDeployVersionInfoReplyValidationError{
					field:  "DeployVersion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeployVersion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeDeployVersionInfoReplyValidationError{
				field:  "DeployVersion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeployVersionPrev()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeDeployVersionInfoReplyValidationError{
					field:  "DeployVersionPrev",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeDeployVersionInfoReplyValidationError{
					field:  "DeployVersionPrev",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeployVersionPrev()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeDeployVersionInfoReplyValidationError{
				field:  "DeployVersionPrev",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DescribeDeployVersionInfoReplyMultiError(errors)
	}

	return nil
}

// DescribeDeployVersionInfoReplyMultiError is an error wrapping multiple
// validation errors returned by DescribeDeployVersionInfoReply.ValidateAll()
// if the designated constraints aren't met.
type DescribeDeployVersionInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeDeployVersionInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeDeployVersionInfoReplyMultiError) AllErrors() []error { return m }

// DescribeDeployVersionInfoReplyValidationError is the validation error
// returned by DescribeDeployVersionInfoReply.Validate if the designated
// constraints aren't met.
type DescribeDeployVersionInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeDeployVersionInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeDeployVersionInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeDeployVersionInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeDeployVersionInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeDeployVersionInfoReplyValidationError) ErrorName() string {
	return "DescribeDeployVersionInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeDeployVersionInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeDeployVersionInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeDeployVersionInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeDeployVersionInfoReplyValidationError{}

// Validate checks the field values on CreateCvesselMetadataReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCvesselMetadataReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCvesselMetadataReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCvesselMetadataReqMultiError, or nil if none found.
func (m *CreateCvesselMetadataReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCvesselMetadataReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetVersion() != "" {

		if !_CreateCvesselMetadataReq_Version_Pattern.MatchString(m.GetVersion()) {
			err := CreateCvesselMetadataReqValidationError{
				field:  "Version",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVersionType() != "" {

	}

	if m.GetSnapshotVersion() != "" {

		if !_CreateCvesselMetadataReq_SnapshotVersion_Pattern.MatchString(m.GetSnapshotVersion()) {
			err := CreateCvesselMetadataReqValidationError{
				field:  "SnapshotVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetPreVersion() != "" {

		if !_CreateCvesselMetadataReq_PreVersion_Pattern.MatchString(m.GetPreVersion()) {
			err := CreateCvesselMetadataReqValidationError{
				field:  "PreVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetJdstackVersion() != "" {

		if !_CreateCvesselMetadataReq_JdstackVersion_Pattern.MatchString(m.GetJdstackVersion()) {
			err := CreateCvesselMetadataReqValidationError{
				field:  "JdstackVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Data

	// no validation rules for ForceOverride

	if m.GetSnapshotGeneratedTime() != "" {

		if !_CreateCvesselMetadataReq_SnapshotGeneratedTime_Pattern.MatchString(m.GetSnapshotGeneratedTime()) {
			err := CreateCvesselMetadataReqValidationError{
				field:  "SnapshotGeneratedTime",
				reason: "value does not match regex pattern \"^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d{3})?Z$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateCvesselMetadataReqMultiError(errors)
	}

	return nil
}

// CreateCvesselMetadataReqMultiError is an error wrapping multiple validation
// errors returned by CreateCvesselMetadataReq.ValidateAll() if the designated
// constraints aren't met.
type CreateCvesselMetadataReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCvesselMetadataReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCvesselMetadataReqMultiError) AllErrors() []error { return m }

// CreateCvesselMetadataReqValidationError is the validation error returned by
// CreateCvesselMetadataReq.Validate if the designated constraints aren't met.
type CreateCvesselMetadataReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCvesselMetadataReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCvesselMetadataReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCvesselMetadataReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCvesselMetadataReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCvesselMetadataReqValidationError) ErrorName() string {
	return "CreateCvesselMetadataReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCvesselMetadataReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCvesselMetadataReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCvesselMetadataReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCvesselMetadataReqValidationError{}

var _CreateCvesselMetadataReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _CreateCvesselMetadataReq_SnapshotVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _CreateCvesselMetadataReq_PreVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _CreateCvesselMetadataReq_JdstackVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _CreateCvesselMetadataReq_SnapshotGeneratedTime_Pattern = regexp.MustCompile("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?Z$")

// Validate checks the field values on ModifyCvesselMetadataReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyCvesselMetadataReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyCvesselMetadataReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyCvesselMetadataReqMultiError, or nil if none found.
func (m *ModifyCvesselMetadataReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyCvesselMetadataReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetVersion() != "" {

		if !_ModifyCvesselMetadataReq_Version_Pattern.MatchString(m.GetVersion()) {
			err := ModifyCvesselMetadataReqValidationError{
				field:  "Version",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVersionType() != "" {

	}

	if m.GetSnapshotVersion() != "" {

		if !_ModifyCvesselMetadataReq_SnapshotVersion_Pattern.MatchString(m.GetSnapshotVersion()) {
			err := ModifyCvesselMetadataReqValidationError{
				field:  "SnapshotVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetPreVersion() != "" {

		if !_ModifyCvesselMetadataReq_PreVersion_Pattern.MatchString(m.GetPreVersion()) {
			err := ModifyCvesselMetadataReqValidationError{
				field:  "PreVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetJdstackVersion() != "" {

		if !_ModifyCvesselMetadataReq_JdstackVersion_Pattern.MatchString(m.GetJdstackVersion()) {
			err := ModifyCvesselMetadataReqValidationError{
				field:  "JdstackVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Data

	if len(errors) > 0 {
		return ModifyCvesselMetadataReqMultiError(errors)
	}

	return nil
}

// ModifyCvesselMetadataReqMultiError is an error wrapping multiple validation
// errors returned by ModifyCvesselMetadataReq.ValidateAll() if the designated
// constraints aren't met.
type ModifyCvesselMetadataReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyCvesselMetadataReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyCvesselMetadataReqMultiError) AllErrors() []error { return m }

// ModifyCvesselMetadataReqValidationError is the validation error returned by
// ModifyCvesselMetadataReq.Validate if the designated constraints aren't met.
type ModifyCvesselMetadataReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyCvesselMetadataReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyCvesselMetadataReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyCvesselMetadataReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyCvesselMetadataReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyCvesselMetadataReqValidationError) ErrorName() string {
	return "ModifyCvesselMetadataReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyCvesselMetadataReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyCvesselMetadataReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyCvesselMetadataReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyCvesselMetadataReqValidationError{}

var _ModifyCvesselMetadataReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _ModifyCvesselMetadataReq_SnapshotVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _ModifyCvesselMetadataReq_PreVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _ModifyCvesselMetadataReq_JdstackVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

// Validate checks the field values on DeleteCvesselMetadataReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCvesselMetadataReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCvesselMetadataReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCvesselMetadataReqMultiError, or nil if none found.
func (m *DeleteCvesselMetadataReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCvesselMetadataReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetVersion() != "" {

		if !_DeleteCvesselMetadataReq_Version_Pattern.MatchString(m.GetVersion()) {
			err := DeleteCvesselMetadataReqValidationError{
				field:  "Version",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetVersionType() != "" {

	}

	if m.GetJdstackVersion() != "" {

		if !_DeleteCvesselMetadataReq_JdstackVersion_Pattern.MatchString(m.GetJdstackVersion()) {
			err := DeleteCvesselMetadataReqValidationError{
				field:  "JdstackVersion",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DeleteCvesselMetadataReqMultiError(errors)
	}

	return nil
}

// DeleteCvesselMetadataReqMultiError is an error wrapping multiple validation
// errors returned by DeleteCvesselMetadataReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteCvesselMetadataReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCvesselMetadataReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCvesselMetadataReqMultiError) AllErrors() []error { return m }

// DeleteCvesselMetadataReqValidationError is the validation error returned by
// DeleteCvesselMetadataReq.Validate if the designated constraints aren't met.
type DeleteCvesselMetadataReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCvesselMetadataReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCvesselMetadataReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCvesselMetadataReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCvesselMetadataReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCvesselMetadataReqValidationError) ErrorName() string {
	return "DeleteCvesselMetadataReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCvesselMetadataReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCvesselMetadataReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCvesselMetadataReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCvesselMetadataReqValidationError{}

var _DeleteCvesselMetadataReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

var _DeleteCvesselMetadataReq_JdstackVersion_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

// Validate checks the field values on CreateCvesselMetadataReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCvesselMetadataReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCvesselMetadataReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCvesselMetadataReplyMultiError, or nil if none found.
func (m *CreateCvesselMetadataReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCvesselMetadataReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return CreateCvesselMetadataReplyMultiError(errors)
	}

	return nil
}

// CreateCvesselMetadataReplyMultiError is an error wrapping multiple
// validation errors returned by CreateCvesselMetadataReply.ValidateAll() if
// the designated constraints aren't met.
type CreateCvesselMetadataReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCvesselMetadataReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCvesselMetadataReplyMultiError) AllErrors() []error { return m }

// CreateCvesselMetadataReplyValidationError is the validation error returned
// by CreateCvesselMetadataReply.Validate if the designated constraints aren't met.
type CreateCvesselMetadataReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCvesselMetadataReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCvesselMetadataReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCvesselMetadataReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCvesselMetadataReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCvesselMetadataReplyValidationError) ErrorName() string {
	return "CreateCvesselMetadataReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCvesselMetadataReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCvesselMetadataReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCvesselMetadataReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCvesselMetadataReplyValidationError{}

// Validate checks the field values on ModifyCvesselMetadataReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyCvesselMetadataReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyCvesselMetadataReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyCvesselMetadataReplyMultiError, or nil if none found.
func (m *ModifyCvesselMetadataReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyCvesselMetadataReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return ModifyCvesselMetadataReplyMultiError(errors)
	}

	return nil
}

// ModifyCvesselMetadataReplyMultiError is an error wrapping multiple
// validation errors returned by ModifyCvesselMetadataReply.ValidateAll() if
// the designated constraints aren't met.
type ModifyCvesselMetadataReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyCvesselMetadataReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyCvesselMetadataReplyMultiError) AllErrors() []error { return m }

// ModifyCvesselMetadataReplyValidationError is the validation error returned
// by ModifyCvesselMetadataReply.Validate if the designated constraints aren't met.
type ModifyCvesselMetadataReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyCvesselMetadataReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyCvesselMetadataReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyCvesselMetadataReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyCvesselMetadataReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyCvesselMetadataReplyValidationError) ErrorName() string {
	return "ModifyCvesselMetadataReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyCvesselMetadataReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyCvesselMetadataReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyCvesselMetadataReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyCvesselMetadataReplyValidationError{}

// Validate checks the field values on DeleteCvesselMetadataReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCvesselMetadataReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCvesselMetadataReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCvesselMetadataReplyMultiError, or nil if none found.
func (m *DeleteCvesselMetadataReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCvesselMetadataReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return DeleteCvesselMetadataReplyMultiError(errors)
	}

	return nil
}

// DeleteCvesselMetadataReplyMultiError is an error wrapping multiple
// validation errors returned by DeleteCvesselMetadataReply.ValidateAll() if
// the designated constraints aren't met.
type DeleteCvesselMetadataReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCvesselMetadataReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCvesselMetadataReplyMultiError) AllErrors() []error { return m }

// DeleteCvesselMetadataReplyValidationError is the validation error returned
// by DeleteCvesselMetadataReply.Validate if the designated constraints aren't met.
type DeleteCvesselMetadataReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCvesselMetadataReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCvesselMetadataReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCvesselMetadataReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCvesselMetadataReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCvesselMetadataReplyValidationError) ErrorName() string {
	return "DeleteCvesselMetadataReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCvesselMetadataReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCvesselMetadataReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCvesselMetadataReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCvesselMetadataReplyValidationError{}

// Validate checks the field values on DescribeReleaseConfigFileReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeReleaseConfigFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeReleaseConfigFileReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeReleaseConfigFileReqMultiError, or nil if none found.
func (m *DescribeReleaseConfigFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeReleaseConfigFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for CloudType

	// no validation rules for FileName

	if len(errors) > 0 {
		return DescribeReleaseConfigFileReqMultiError(errors)
	}

	return nil
}

// DescribeReleaseConfigFileReqMultiError is an error wrapping multiple
// validation errors returned by DescribeReleaseConfigFileReq.ValidateAll() if
// the designated constraints aren't met.
type DescribeReleaseConfigFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeReleaseConfigFileReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeReleaseConfigFileReqMultiError) AllErrors() []error { return m }

// DescribeReleaseConfigFileReqValidationError is the validation error returned
// by DescribeReleaseConfigFileReq.Validate if the designated constraints
// aren't met.
type DescribeReleaseConfigFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeReleaseConfigFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeReleaseConfigFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeReleaseConfigFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeReleaseConfigFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeReleaseConfigFileReqValidationError) ErrorName() string {
	return "DescribeReleaseConfigFileReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeReleaseConfigFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeReleaseConfigFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeReleaseConfigFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeReleaseConfigFileReqValidationError{}

// Validate checks the field values on DescribeReleaseConfigFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeReleaseConfigFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeReleaseConfigFileReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribeReleaseConfigFileReplyMultiError, or nil if none found.
func (m *DescribeReleaseConfigFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeReleaseConfigFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for RelativePath

	// no validation rules for Content

	if len(errors) > 0 {
		return DescribeReleaseConfigFileReplyMultiError(errors)
	}

	return nil
}

// DescribeReleaseConfigFileReplyMultiError is an error wrapping multiple
// validation errors returned by DescribeReleaseConfigFileReply.ValidateAll()
// if the designated constraints aren't met.
type DescribeReleaseConfigFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeReleaseConfigFileReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeReleaseConfigFileReplyMultiError) AllErrors() []error { return m }

// DescribeReleaseConfigFileReplyValidationError is the validation error
// returned by DescribeReleaseConfigFileReply.Validate if the designated
// constraints aren't met.
type DescribeReleaseConfigFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeReleaseConfigFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeReleaseConfigFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeReleaseConfigFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeReleaseConfigFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeReleaseConfigFileReplyValidationError) ErrorName() string {
	return "DescribeReleaseConfigFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeReleaseConfigFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeReleaseConfigFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeReleaseConfigFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeReleaseConfigFileReplyValidationError{}

// Validate checks the field values on DescribeReleaseConfigFilesReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeReleaseConfigFilesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeReleaseConfigFilesReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribeReleaseConfigFilesReplyMultiError, or nil if none found.
func (m *DescribeReleaseConfigFilesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeReleaseConfigFilesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeReleaseConfigFilesReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeReleaseConfigFilesReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeReleaseConfigFilesReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return DescribeReleaseConfigFilesReplyMultiError(errors)
	}

	return nil
}

// DescribeReleaseConfigFilesReplyMultiError is an error wrapping multiple
// validation errors returned by DescribeReleaseConfigFilesReply.ValidateAll()
// if the designated constraints aren't met.
type DescribeReleaseConfigFilesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeReleaseConfigFilesReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeReleaseConfigFilesReplyMultiError) AllErrors() []error { return m }

// DescribeReleaseConfigFilesReplyValidationError is the validation error
// returned by DescribeReleaseConfigFilesReply.Validate if the designated
// constraints aren't met.
type DescribeReleaseConfigFilesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeReleaseConfigFilesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeReleaseConfigFilesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeReleaseConfigFilesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeReleaseConfigFilesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeReleaseConfigFilesReplyValidationError) ErrorName() string {
	return "DescribeReleaseConfigFilesReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeReleaseConfigFilesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeReleaseConfigFilesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeReleaseConfigFilesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeReleaseConfigFilesReplyValidationError{}

// Validate checks the field values on ModifyDependenciesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyDependenciesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyDependenciesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyDependenciesReqMultiError, or nil if none found.
func (m *ModifyDependenciesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyDependenciesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetVersion()) < 1 {
		err := ModifyDependenciesReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ModifyDependenciesReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := ModifyDependenciesReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetProducts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ModifyDependenciesReqValidationError{
						field:  fmt.Sprintf("Products[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ModifyDependenciesReqValidationError{
						field:  fmt.Sprintf("Products[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ModifyDependenciesReqValidationError{
					field:  fmt.Sprintf("Products[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ModifyDependenciesReqMultiError(errors)
	}

	return nil
}

// ModifyDependenciesReqMultiError is an error wrapping multiple validation
// errors returned by ModifyDependenciesReq.ValidateAll() if the designated
// constraints aren't met.
type ModifyDependenciesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyDependenciesReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyDependenciesReqMultiError) AllErrors() []error { return m }

// ModifyDependenciesReqValidationError is the validation error returned by
// ModifyDependenciesReq.Validate if the designated constraints aren't met.
type ModifyDependenciesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyDependenciesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyDependenciesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyDependenciesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyDependenciesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyDependenciesReqValidationError) ErrorName() string {
	return "ModifyDependenciesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyDependenciesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyDependenciesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyDependenciesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyDependenciesReqValidationError{}

var _ModifyDependenciesReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

// Validate checks the field values on ProductInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProductInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProductInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProductInfoMultiError, or
// nil if none found.
func (m *ProductInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Required

	if len(errors) > 0 {
		return ProductInfoMultiError(errors)
	}

	return nil
}

// ProductInfoMultiError is an error wrapping multiple validation errors
// returned by ProductInfo.ValidateAll() if the designated constraints aren't met.
type ProductInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductInfoMultiError) AllErrors() []error { return m }

// ProductInfoValidationError is the validation error returned by
// ProductInfo.Validate if the designated constraints aren't met.
type ProductInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProductInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductInfoValidationError) ErrorName() string { return "ProductInfoValidationError" }

// Error satisfies the builtin error interface
func (e ProductInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductInfoValidationError{}

// Validate checks the field values on ModifyDependenciesReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyDependenciesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyDependenciesReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyDependenciesReplyMultiError, or nil if none found.
func (m *ModifyDependenciesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyDependenciesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	if len(errors) > 0 {
		return ModifyDependenciesReplyMultiError(errors)
	}

	return nil
}

// ModifyDependenciesReplyMultiError is an error wrapping multiple validation
// errors returned by ModifyDependenciesReply.ValidateAll() if the designated
// constraints aren't met.
type ModifyDependenciesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyDependenciesReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyDependenciesReplyMultiError) AllErrors() []error { return m }

// ModifyDependenciesReplyValidationError is the validation error returned by
// ModifyDependenciesReply.Validate if the designated constraints aren't met.
type ModifyDependenciesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyDependenciesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyDependenciesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyDependenciesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyDependenciesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyDependenciesReplyValidationError) ErrorName() string {
	return "ModifyDependenciesReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyDependenciesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyDependenciesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyDependenciesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyDependenciesReplyValidationError{}

// Validate checks the field values on CreateSqlExportToS3Req with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSqlExportToS3Req) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSqlExportToS3Req with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSqlExportToS3ReqMultiError, or nil if none found.
func (m *CreateSqlExportToS3Req) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSqlExportToS3Req) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetVersion()) < 1 {
		err := CreateSqlExportToS3ReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateSqlExportToS3Req_Version_Pattern.MatchString(m.GetVersion()) {
		err := CreateSqlExportToS3ReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z._-]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetVersionType() != "" {

	}

	// no validation rules for IncludePrevVersion

	if len(errors) > 0 {
		return CreateSqlExportToS3ReqMultiError(errors)
	}

	return nil
}

// CreateSqlExportToS3ReqMultiError is an error wrapping multiple validation
// errors returned by CreateSqlExportToS3Req.ValidateAll() if the designated
// constraints aren't met.
type CreateSqlExportToS3ReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSqlExportToS3ReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSqlExportToS3ReqMultiError) AllErrors() []error { return m }

// CreateSqlExportToS3ReqValidationError is the validation error returned by
// CreateSqlExportToS3Req.Validate if the designated constraints aren't met.
type CreateSqlExportToS3ReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSqlExportToS3ReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSqlExportToS3ReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSqlExportToS3ReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSqlExportToS3ReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSqlExportToS3ReqValidationError) ErrorName() string {
	return "CreateSqlExportToS3ReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSqlExportToS3ReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSqlExportToS3Req.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSqlExportToS3ReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSqlExportToS3ReqValidationError{}

var _CreateSqlExportToS3Req_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z._-]+$")

// Validate checks the field values on CreateSqlExportToS3Reply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSqlExportToS3Reply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSqlExportToS3Reply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSqlExportToS3ReplyMultiError, or nil if none found.
func (m *CreateSqlExportToS3Reply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSqlExportToS3Reply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for S3Bucket

	// no validation rules for S3ObjectKey

	// no validation rules for Status

	// no validation rules for Message

	if len(errors) > 0 {
		return CreateSqlExportToS3ReplyMultiError(errors)
	}

	return nil
}

// CreateSqlExportToS3ReplyMultiError is an error wrapping multiple validation
// errors returned by CreateSqlExportToS3Reply.ValidateAll() if the designated
// constraints aren't met.
type CreateSqlExportToS3ReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSqlExportToS3ReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSqlExportToS3ReplyMultiError) AllErrors() []error { return m }

// CreateSqlExportToS3ReplyValidationError is the validation error returned by
// CreateSqlExportToS3Reply.Validate if the designated constraints aren't met.
type CreateSqlExportToS3ReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSqlExportToS3ReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSqlExportToS3ReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSqlExportToS3ReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSqlExportToS3ReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSqlExportToS3ReplyValidationError) ErrorName() string {
	return "CreateSqlExportToS3ReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSqlExportToS3ReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSqlExportToS3Reply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSqlExportToS3ReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSqlExportToS3ReplyValidationError{}
