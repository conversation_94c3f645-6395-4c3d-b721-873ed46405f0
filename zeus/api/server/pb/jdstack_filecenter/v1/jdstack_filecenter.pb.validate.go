// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: jdstack_filecenter.proto

package pbJdstackFilecenter

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FilePropertiesRes with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FilePropertiesRes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilePropertiesRes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilePropertiesResMultiError, or nil if none found.
func (m *FilePropertiesRes) ValidateAll() error {
	return m.validate(true)
}

func (m *FilePropertiesRes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetIdentifier()) < 1 {
		err := FilePropertiesResValidationError{
			field:  "Identifier",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _FilePropertiesRes_DeployMode_InLookup[m.GetDeployMode()]; !ok {
		err := FilePropertiesResValidationError{
			field:  "DeployMode",
			reason: "value must be in list [skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Filename

	if len(m.GetAddress()) < 1 {
		err := FilePropertiesResValidationError{
			field:  "Address",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ModuleName

	// no validation rules for ImageTags

	// no validation rules for StorageMode

	// no validation rules for StorageBucket

	// no validation rules for RepoKind

	// no validation rules for CommitId

	if m.GetOs() != "" {

		if _, ok := _FilePropertiesRes_Os_InLookup[m.GetOs()]; !ok {
			err := FilePropertiesResValidationError{
				field:  "Os",
				reason: "value must be in list [centos openEuler kylin]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := _FilePropertiesRes_Arch_InLookup[m.GetArch()]; !ok {
		err := FilePropertiesResValidationError{
			field:  "Arch",
			reason: "value must be in list [x86_64 arm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FilePropertiesResMultiError(errors)
	}

	return nil
}

// FilePropertiesResMultiError is an error wrapping multiple validation errors
// returned by FilePropertiesRes.ValidateAll() if the designated constraints
// aren't met.
type FilePropertiesResMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilePropertiesResMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilePropertiesResMultiError) AllErrors() []error { return m }

// FilePropertiesResValidationError is the validation error returned by
// FilePropertiesRes.Validate if the designated constraints aren't met.
type FilePropertiesResValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilePropertiesResValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilePropertiesResValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilePropertiesResValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilePropertiesResValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilePropertiesResValidationError) ErrorName() string {
	return "FilePropertiesResValidationError"
}

// Error satisfies the builtin error interface
func (e FilePropertiesResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilePropertiesRes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilePropertiesResValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilePropertiesResValidationError{}

var _FilePropertiesRes_DeployMode_InLookup = map[string]struct{}{
	"skywing_container": {},
	"skywing_package":   {},
	"mate_container":    {},
	"shell":             {},
	"helm":              {},
	"rpm":               {},
	"mate_bin":          {},
	"mate_v2":           {},
	"iaas_image":        {},
	"xingyun_container": {},
	"xingyun_package":   {},
}

var _FilePropertiesRes_Os_InLookup = map[string]struct{}{
	"centos":    {},
	"openEuler": {},
	"kylin":     {},
}

var _FilePropertiesRes_Arch_InLookup = map[string]struct{}{
	"x86_64": {},
	"arm":    {},
}

// Validate checks the field values on CreateUploadTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUploadTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUploadTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUploadTaskReqMultiError, or nil if none found.
func (m *CreateUploadTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUploadTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetVersion()) < 1 {
		err := CreateUploadTaskReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateUploadTaskReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := CreateUploadTaskReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsStore

	if len(m.GetServiceCode()) < 1 {
		err := CreateUploadTaskReqValidationError{
			field:  "ServiceCode",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AppName

	if _, ok := _CreateUploadTaskReq_Arch_InLookup[m.GetArch()]; !ok {
		err := CreateUploadTaskReqValidationError{
			field:  "Arch",
			reason: "value must be in list [x86_64 arm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateUploadTaskReq_Os_InLookup[m.GetOs()]; !ok {
		err := CreateUploadTaskReqValidationError{
			field:  "Os",
			reason: "value must be in list [centos openEuler kylin]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFiles()) < 1 {
		err := CreateUploadTaskReqValidationError{
			field:  "Files",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateUploadTaskReqValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateUploadTaskReqValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateUploadTaskReqValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetEnvName() != "" {

		if !_CreateUploadTaskReq_EnvName_Pattern.MatchString(m.GetEnvName()) {
			err := CreateUploadTaskReqValidationError{
				field:  "EnvName",
				reason: "value does not match regex pattern \"^[0-9a-zA-Z.-_]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateUploadTaskReqMultiError(errors)
	}

	return nil
}

// CreateUploadTaskReqMultiError is an error wrapping multiple validation
// errors returned by CreateUploadTaskReq.ValidateAll() if the designated
// constraints aren't met.
type CreateUploadTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUploadTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUploadTaskReqMultiError) AllErrors() []error { return m }

// CreateUploadTaskReqValidationError is the validation error returned by
// CreateUploadTaskReq.Validate if the designated constraints aren't met.
type CreateUploadTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUploadTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUploadTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUploadTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUploadTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUploadTaskReqValidationError) ErrorName() string {
	return "CreateUploadTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUploadTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUploadTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUploadTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUploadTaskReqValidationError{}

var _CreateUploadTaskReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

var _CreateUploadTaskReq_Arch_InLookup = map[string]struct{}{
	"x86_64": {},
	"arm":    {},
}

var _CreateUploadTaskReq_Os_InLookup = map[string]struct{}{
	"centos":    {},
	"openEuler": {},
	"kylin":     {},
}

var _CreateUploadTaskReq_EnvName_Pattern = regexp.MustCompile("^[0-9a-zA-Z.-_]+$")

// Validate checks the field values on CreateFileDownloadReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFileDownloadReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFileDownloadReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFileDownloadReqMultiError, or nil if none found.
func (m *CreateFileDownloadReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFileDownloadReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for ServiceCode

	if _, ok := _CreateFileDownloadReq_Arch_InLookup[m.GetArch()]; !ok {
		err := CreateFileDownloadReqValidationError{
			field:  "Arch",
			reason: "value must be in list [amd64 arm64]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateFileDownloadReq_FileType_InLookup[m.GetFileType()]; !ok {
		err := CreateFileDownloadReqValidationError{
			field:  "FileType",
			reason: "value must be in list [image out_image rpm os_file package helm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AppVersionID

	if len(errors) > 0 {
		return CreateFileDownloadReqMultiError(errors)
	}

	return nil
}

// CreateFileDownloadReqMultiError is an error wrapping multiple validation
// errors returned by CreateFileDownloadReq.ValidateAll() if the designated
// constraints aren't met.
type CreateFileDownloadReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFileDownloadReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFileDownloadReqMultiError) AllErrors() []error { return m }

// CreateFileDownloadReqValidationError is the validation error returned by
// CreateFileDownloadReq.Validate if the designated constraints aren't met.
type CreateFileDownloadReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFileDownloadReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFileDownloadReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFileDownloadReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFileDownloadReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFileDownloadReqValidationError) ErrorName() string {
	return "CreateFileDownloadReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFileDownloadReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFileDownloadReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFileDownloadReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFileDownloadReqValidationError{}

var _CreateFileDownloadReq_Arch_InLookup = map[string]struct{}{
	"amd64": {},
	"arm64": {},
}

var _CreateFileDownloadReq_FileType_InLookup = map[string]struct{}{
	"image":     {},
	"out_image": {},
	"rpm":       {},
	"os_file":   {},
	"package":   {},
	"helm":      {},
}

// Validate checks the field values on DescribeFileDownloadReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeFileDownloadReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeFileDownloadReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeFileDownloadReplyMultiError, or nil if none found.
func (m *DescribeFileDownloadReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeFileDownloadReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for FileName

	// no validation rules for Md5

	// no validation rules for ErrorMsg

	if len(errors) > 0 {
		return DescribeFileDownloadReplyMultiError(errors)
	}

	return nil
}

// DescribeFileDownloadReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeFileDownloadReply.ValidateAll() if the
// designated constraints aren't met.
type DescribeFileDownloadReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeFileDownloadReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeFileDownloadReplyMultiError) AllErrors() []error { return m }

// DescribeFileDownloadReplyValidationError is the validation error returned by
// DescribeFileDownloadReply.Validate if the designated constraints aren't met.
type DescribeFileDownloadReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeFileDownloadReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeFileDownloadReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeFileDownloadReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeFileDownloadReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeFileDownloadReplyValidationError) ErrorName() string {
	return "DescribeFileDownloadReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeFileDownloadReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeFileDownloadReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeFileDownloadReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeFileDownloadReplyValidationError{}

// Validate checks the field values on ModifyArtifactAndMetadataByDeployReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ModifyArtifactAndMetadataByDeployReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyArtifactAndMetadataByDeployReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ModifyArtifactAndMetadataByDeployReqMultiError, or nil if none found.
func (m *ModifyArtifactAndMetadataByDeployReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyArtifactAndMetadataByDeployReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CloudType

	if len(m.GetVersion()) < 1 {
		err := ModifyArtifactAndMetadataByDeployReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ModifyArtifactAndMetadataByDeployReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := ModifyArtifactAndMetadataByDeployReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ServiceCode

	// no validation rules for Category

	// no validation rules for ProductCode

	// no validation rules for ProductName

	if all {
		switch v := interface{}(m.GetAppInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyArtifactAndMetadataByDeployReqValidationError{
					field:  "AppInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyArtifactAndMetadataByDeployReqValidationError{
					field:  "AppInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAppInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyArtifactAndMetadataByDeployReqValidationError{
				field:  "AppInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyArtifactAndMetadataByDeployReqMultiError(errors)
	}

	return nil
}

// ModifyArtifactAndMetadataByDeployReqMultiError is an error wrapping multiple
// validation errors returned by
// ModifyArtifactAndMetadataByDeployReq.ValidateAll() if the designated
// constraints aren't met.
type ModifyArtifactAndMetadataByDeployReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyArtifactAndMetadataByDeployReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyArtifactAndMetadataByDeployReqMultiError) AllErrors() []error { return m }

// ModifyArtifactAndMetadataByDeployReqValidationError is the validation error
// returned by ModifyArtifactAndMetadataByDeployReq.Validate if the designated
// constraints aren't met.
type ModifyArtifactAndMetadataByDeployReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyArtifactAndMetadataByDeployReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyArtifactAndMetadataByDeployReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyArtifactAndMetadataByDeployReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyArtifactAndMetadataByDeployReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyArtifactAndMetadataByDeployReqValidationError) ErrorName() string {
	return "ModifyArtifactAndMetadataByDeployReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyArtifactAndMetadataByDeployReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyArtifactAndMetadataByDeployReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyArtifactAndMetadataByDeployReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyArtifactAndMetadataByDeployReqValidationError{}

var _ModifyArtifactAndMetadataByDeployReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

// Validate checks the field values on ModifyArtifactAndMetadataBySealReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ModifyArtifactAndMetadataBySealReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyArtifactAndMetadataBySealReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ModifyArtifactAndMetadataBySealReqMultiError, or nil if none found.
func (m *ModifyArtifactAndMetadataBySealReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyArtifactAndMetadataBySealReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CloudType

	if len(m.GetVersion()) < 1 {
		err := ModifyArtifactAndMetadataBySealReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ModifyArtifactAndMetadataBySealReq_Version_Pattern.MatchString(m.GetVersion()) {
		err := ModifyArtifactAndMetadataBySealReqValidationError{
			field:  "Version",
			reason: "value does not match regex pattern \"^[0-9a-zA-Z.]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ServiceCode

	// no validation rules for Category

	// no validation rules for ProductCode

	// no validation rules for ProductName

	for idx, item := range m.GetAppInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ModifyArtifactAndMetadataBySealReqValidationError{
						field:  fmt.Sprintf("AppInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ModifyArtifactAndMetadataBySealReqValidationError{
						field:  fmt.Sprintf("AppInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ModifyArtifactAndMetadataBySealReqValidationError{
					field:  fmt.Sprintf("AppInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProductYaml

	for idx, item := range m.GetAdlFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ModifyArtifactAndMetadataBySealReqValidationError{
						field:  fmt.Sprintf("AdlFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ModifyArtifactAndMetadataBySealReqValidationError{
						field:  fmt.Sprintf("AdlFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ModifyArtifactAndMetadataBySealReqValidationError{
					field:  fmt.Sprintf("AdlFiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBusinessMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyArtifactAndMetadataBySealReqValidationError{
					field:  "BusinessMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyArtifactAndMetadataBySealReqValidationError{
					field:  "BusinessMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBusinessMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyArtifactAndMetadataBySealReqValidationError{
				field:  "BusinessMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyArtifactAndMetadataBySealReqMultiError(errors)
	}

	return nil
}

// ModifyArtifactAndMetadataBySealReqMultiError is an error wrapping multiple
// validation errors returned by
// ModifyArtifactAndMetadataBySealReq.ValidateAll() if the designated
// constraints aren't met.
type ModifyArtifactAndMetadataBySealReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyArtifactAndMetadataBySealReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyArtifactAndMetadataBySealReqMultiError) AllErrors() []error { return m }

// ModifyArtifactAndMetadataBySealReqValidationError is the validation error
// returned by ModifyArtifactAndMetadataBySealReq.Validate if the designated
// constraints aren't met.
type ModifyArtifactAndMetadataBySealReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyArtifactAndMetadataBySealReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyArtifactAndMetadataBySealReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyArtifactAndMetadataBySealReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyArtifactAndMetadataBySealReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyArtifactAndMetadataBySealReqValidationError) ErrorName() string {
	return "ModifyArtifactAndMetadataBySealReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyArtifactAndMetadataBySealReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyArtifactAndMetadataBySealReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyArtifactAndMetadataBySealReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyArtifactAndMetadataBySealReqValidationError{}

var _ModifyArtifactAndMetadataBySealReq_Version_Pattern = regexp.MustCompile("^[0-9a-zA-Z.]+$")

// Validate checks the field values on DescribeArtifactAndMetadataBySealReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DescribeArtifactAndMetadataBySealReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DescribeArtifactAndMetadataBySealReply with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DescribeArtifactAndMetadataBySealReplyMultiError, or nil if none found.
func (m *DescribeArtifactAndMetadataBySealReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeArtifactAndMetadataBySealReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	// no validation rules for Message

	if len(errors) > 0 {
		return DescribeArtifactAndMetadataBySealReplyMultiError(errors)
	}

	return nil
}

// DescribeArtifactAndMetadataBySealReplyMultiError is an error wrapping
// multiple validation errors returned by
// DescribeArtifactAndMetadataBySealReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeArtifactAndMetadataBySealReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeArtifactAndMetadataBySealReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeArtifactAndMetadataBySealReplyMultiError) AllErrors() []error { return m }

// DescribeArtifactAndMetadataBySealReplyValidationError is the validation
// error returned by DescribeArtifactAndMetadataBySealReply.Validate if the
// designated constraints aren't met.
type DescribeArtifactAndMetadataBySealReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeArtifactAndMetadataBySealReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeArtifactAndMetadataBySealReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeArtifactAndMetadataBySealReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeArtifactAndMetadataBySealReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeArtifactAndMetadataBySealReplyValidationError) ErrorName() string {
	return "DescribeArtifactAndMetadataBySealReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeArtifactAndMetadataBySealReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeArtifactAndMetadataBySealReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeArtifactAndMetadataBySealReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeArtifactAndMetadataBySealReplyValidationError{}

// Validate checks the field values on ApplicationInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicationInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicationInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicationInfoReqMultiError, or nil if none found.
func (m *ApplicationInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicationInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppCode

	// no validation rules for AppName

	if _, ok := _ApplicationInfoReq_DeployMode_InLookup[m.GetDeployMode()]; !ok {
		err := ApplicationInfoReqValidationError{
			field:  "DeployMode",
			reason: "value must be in list [skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetVirtualGoods() != "" {

		if _, ok := _ApplicationInfoReq_VirtualGoods_InLookup[m.GetVirtualGoods()]; !ok {
			err := ApplicationInfoReqValidationError{
				field:  "VirtualGoods",
				reason: "value must be in list [no yes]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetArtifacts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApplicationInfoReqValidationError{
						field:  fmt.Sprintf("Artifacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApplicationInfoReqValidationError{
						field:  fmt.Sprintf("Artifacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApplicationInfoReqValidationError{
					field:  fmt.Sprintf("Artifacts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AppYaml

	for idx, item := range m.GetAdlFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApplicationInfoReqValidationError{
						field:  fmt.Sprintf("AdlFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApplicationInfoReqValidationError{
						field:  fmt.Sprintf("AdlFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApplicationInfoReqValidationError{
					field:  fmt.Sprintf("AdlFiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ApplicationInfoReqMultiError(errors)
	}

	return nil
}

// ApplicationInfoReqMultiError is an error wrapping multiple validation errors
// returned by ApplicationInfoReq.ValidateAll() if the designated constraints
// aren't met.
type ApplicationInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicationInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicationInfoReqMultiError) AllErrors() []error { return m }

// ApplicationInfoReqValidationError is the validation error returned by
// ApplicationInfoReq.Validate if the designated constraints aren't met.
type ApplicationInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicationInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicationInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicationInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicationInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicationInfoReqValidationError) ErrorName() string {
	return "ApplicationInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicationInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicationInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicationInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicationInfoReqValidationError{}

var _ApplicationInfoReq_DeployMode_InLookup = map[string]struct{}{
	"skywing_container": {},
	"skywing_package":   {},
	"mate_container":    {},
	"shell":             {},
	"helm":              {},
	"rpm":               {},
	"mate_bin":          {},
	"mate_v2":           {},
	"iaas_image":        {},
	"xingyun_container": {},
	"xingyun_package":   {},
}

var _ApplicationInfoReq_VirtualGoods_InLookup = map[string]struct{}{
	"no":  {},
	"yes": {},
}

// Validate checks the field values on ArtifactReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ArtifactReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArtifactReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArtifactReqMultiError, or
// nil if none found.
func (m *ArtifactReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ArtifactReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetIdentifier()) < 1 {
		err := ArtifactReqValidationError{
			field:  "Identifier",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetVersion()) < 1 {
		err := ArtifactReqValidationError{
			field:  "Version",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ArtifactReq_FileType_InLookup[m.GetFileType()]; !ok {
		err := ArtifactReqValidationError{
			field:  "FileType",
			reason: "value must be in list [image out_image rpm os_file package helm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FileName

	// no validation rules for Md5

	if len(m.GetAddress()) < 1 {
		err := ArtifactReqValidationError{
			field:  "Address",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FileStorageMode

	// no validation rules for FileBucket

	// no validation rules for RpmRepo

	// no validation rules for ImageRepo

	// no validation rules for ImageTag

	// no validation rules for PkgModelName

	// no validation rules for PkgVersion

	if _, ok := _ArtifactReq_Os_InLookup[m.GetOs()]; !ok {
		err := ArtifactReqValidationError{
			field:  "Os",
			reason: "value must be in list [centos openeuler22 kylinv10]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ArtifactReq_Arch_InLookup[m.GetArch()]; !ok {
		err := ArtifactReqValidationError{
			field:  "Arch",
			reason: "value must be in list [amd64 arm64]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ArtifactReqMultiError(errors)
	}

	return nil
}

// ArtifactReqMultiError is an error wrapping multiple validation errors
// returned by ArtifactReq.ValidateAll() if the designated constraints aren't met.
type ArtifactReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArtifactReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArtifactReqMultiError) AllErrors() []error { return m }

// ArtifactReqValidationError is the validation error returned by
// ArtifactReq.Validate if the designated constraints aren't met.
type ArtifactReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArtifactReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArtifactReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArtifactReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArtifactReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArtifactReqValidationError) ErrorName() string { return "ArtifactReqValidationError" }

// Error satisfies the builtin error interface
func (e ArtifactReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArtifactReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArtifactReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArtifactReqValidationError{}

var _ArtifactReq_FileType_InLookup = map[string]struct{}{
	"image":     {},
	"out_image": {},
	"rpm":       {},
	"os_file":   {},
	"package":   {},
	"helm":      {},
}

var _ArtifactReq_Os_InLookup = map[string]struct{}{
	"centos":      {},
	"openeuler22": {},
	"kylinv10":    {},
}

var _ArtifactReq_Arch_InLookup = map[string]struct{}{
	"amd64": {},
	"arm64": {},
}

// Validate checks the field values on RegularFileTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegularFileTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegularFileTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegularFileTaskReqMultiError, or nil if none found.
func (m *RegularFileTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RegularFileTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppName

	if _, ok := _RegularFileTaskReq_Architecture_InLookup[m.GetArchitecture()]; !ok {
		err := RegularFileTaskReqValidationError{
			field:  "Architecture",
			reason: "value must be in list [amd64 arm64]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for DestPath

	// no validation rules for FileIdentifier

	// no validation rules for Filename

	// no validation rules for HostOsType

	// no validation rules for Md5

	// no validation rules for Override

	// no validation rules for Region

	// no validation rules for ServiceCode

	// no validation rules for StorageLocation

	// no validation rules for Version

	if len(errors) > 0 {
		return RegularFileTaskReqMultiError(errors)
	}

	return nil
}

// RegularFileTaskReqMultiError is an error wrapping multiple validation errors
// returned by RegularFileTaskReq.ValidateAll() if the designated constraints
// aren't met.
type RegularFileTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegularFileTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegularFileTaskReqMultiError) AllErrors() []error { return m }

// RegularFileTaskReqValidationError is the validation error returned by
// RegularFileTaskReq.Validate if the designated constraints aren't met.
type RegularFileTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegularFileTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegularFileTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegularFileTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegularFileTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegularFileTaskReqValidationError) ErrorName() string {
	return "RegularFileTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e RegularFileTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegularFileTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegularFileTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegularFileTaskReqValidationError{}

var _RegularFileTaskReq_Architecture_InLookup = map[string]struct{}{
	"amd64": {},
	"arm64": {},
}

// Validate checks the field values on RpmTaskReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RpmTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RpmTaskReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RpmTaskReqMultiError, or
// nil if none found.
func (m *RpmTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RpmTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppName

	if _, ok := _RpmTaskReq_Architecture_InLookup[m.GetArchitecture()]; !ok {
		err := RpmTaskReqValidationError{
			field:  "Architecture",
			reason: "value must be in list [amd64 arm64]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for FileIdentifier

	// no validation rules for Filename

	// no validation rules for HostOsType

	// no validation rules for Md5

	// no validation rules for Override

	// no validation rules for Region

	// no validation rules for Repo

	// no validation rules for ServiceCode

	// no validation rules for Version

	if len(errors) > 0 {
		return RpmTaskReqMultiError(errors)
	}

	return nil
}

// RpmTaskReqMultiError is an error wrapping multiple validation errors
// returned by RpmTaskReq.ValidateAll() if the designated constraints aren't met.
type RpmTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RpmTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RpmTaskReqMultiError) AllErrors() []error { return m }

// RpmTaskReqValidationError is the validation error returned by
// RpmTaskReq.Validate if the designated constraints aren't met.
type RpmTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RpmTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RpmTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RpmTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RpmTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RpmTaskReqValidationError) ErrorName() string { return "RpmTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e RpmTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRpmTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RpmTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RpmTaskReqValidationError{}

var _RpmTaskReq_Architecture_InLookup = map[string]struct{}{
	"amd64": {},
	"arm64": {},
}

// Validate checks the field values on SkywingPackageTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SkywingPackageTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SkywingPackageTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SkywingPackageTaskReqMultiError, or nil if none found.
func (m *SkywingPackageTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SkywingPackageTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppName

	if _, ok := _SkywingPackageTaskReq_Architecture_InLookup[m.GetArchitecture()]; !ok {
		err := SkywingPackageTaskReqValidationError{
			field:  "Architecture",
			reason: "value must be in list [amd64 arm64]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Filename

	// no validation rules for HostOsType

	// no validation rules for Md5

	// no validation rules for ModuleName

	// no validation rules for Override

	// no validation rules for PackageVersion

	// no validation rules for Region

	// no validation rules for ServiceCode

	// no validation rules for Version

	if len(errors) > 0 {
		return SkywingPackageTaskReqMultiError(errors)
	}

	return nil
}

// SkywingPackageTaskReqMultiError is an error wrapping multiple validation
// errors returned by SkywingPackageTaskReq.ValidateAll() if the designated
// constraints aren't met.
type SkywingPackageTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SkywingPackageTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SkywingPackageTaskReqMultiError) AllErrors() []error { return m }

// SkywingPackageTaskReqValidationError is the validation error returned by
// SkywingPackageTaskReq.Validate if the designated constraints aren't met.
type SkywingPackageTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SkywingPackageTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SkywingPackageTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SkywingPackageTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SkywingPackageTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SkywingPackageTaskReqValidationError) ErrorName() string {
	return "SkywingPackageTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e SkywingPackageTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSkywingPackageTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SkywingPackageTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SkywingPackageTaskReqValidationError{}

var _SkywingPackageTaskReq_Architecture_InLookup = map[string]struct{}{
	"amd64": {},
	"arm64": {},
}

// Validate checks the field values on DockerImageTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DockerImageTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DockerImageTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DockerImageTaskReqMultiError, or nil if none found.
func (m *DockerImageTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DockerImageTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppName

	if _, ok := _DockerImageTaskReq_Architecture_InLookup[m.GetArchitecture()]; !ok {
		err := DockerImageTaskReqValidationError{
			field:  "Architecture",
			reason: "value must be in list [amd64 arm64]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for FileIdentifier

	// no validation rules for Filename

	// no validation rules for HostOsType

	// no validation rules for Md5

	// no validation rules for Override

	// no validation rules for Region

	// no validation rules for Registry

	// no validation rules for ServiceCode

	// no validation rules for Tag

	// no validation rules for Type

	// no validation rules for Version

	if len(errors) > 0 {
		return DockerImageTaskReqMultiError(errors)
	}

	return nil
}

// DockerImageTaskReqMultiError is an error wrapping multiple validation errors
// returned by DockerImageTaskReq.ValidateAll() if the designated constraints
// aren't met.
type DockerImageTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DockerImageTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DockerImageTaskReqMultiError) AllErrors() []error { return m }

// DockerImageTaskReqValidationError is the validation error returned by
// DockerImageTaskReq.Validate if the designated constraints aren't met.
type DockerImageTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DockerImageTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DockerImageTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DockerImageTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DockerImageTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DockerImageTaskReqValidationError) ErrorName() string {
	return "DockerImageTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e DockerImageTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDockerImageTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DockerImageTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DockerImageTaskReqValidationError{}

var _DockerImageTaskReq_Architecture_InLookup = map[string]struct{}{
	"amd64": {},
	"arm64": {},
}

// Validate checks the field values on DescribeCommonReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeCommonReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeCommonReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeCommonReplyMultiError, or nil if none found.
func (m *DescribeCommonReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeCommonReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	if len(errors) > 0 {
		return DescribeCommonReplyMultiError(errors)
	}

	return nil
}

// DescribeCommonReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeCommonReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeCommonReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeCommonReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeCommonReplyMultiError) AllErrors() []error { return m }

// DescribeCommonReplyValidationError is the validation error returned by
// DescribeCommonReply.Validate if the designated constraints aren't met.
type DescribeCommonReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeCommonReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeCommonReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeCommonReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeCommonReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeCommonReplyValidationError) ErrorName() string {
	return "DescribeCommonReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeCommonReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeCommonReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeCommonReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeCommonReplyValidationError{}

// Validate checks the field values on DescribeTaskReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribeTaskReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeTaskReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeTaskReplyMultiError, or nil if none found.
func (m *DescribeTaskReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeTaskReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeTaskReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeTaskReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeTaskReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if len(errors) > 0 {
		return DescribeTaskReplyMultiError(errors)
	}

	return nil
}

// DescribeTaskReplyMultiError is an error wrapping multiple validation errors
// returned by DescribeTaskReply.ValidateAll() if the designated constraints
// aren't met.
type DescribeTaskReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeTaskReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeTaskReplyMultiError) AllErrors() []error { return m }

// DescribeTaskReplyValidationError is the validation error returned by
// DescribeTaskReply.Validate if the designated constraints aren't met.
type DescribeTaskReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeTaskReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeTaskReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeTaskReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeTaskReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeTaskReplyValidationError) ErrorName() string {
	return "DescribeTaskReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeTaskReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeTaskReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeTaskReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeTaskReplyValidationError{}

// Validate checks the field values on TaskInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskInfoMultiError, or nil
// if none found.
func (m *TaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskID

	if len(errors) > 0 {
		return TaskInfoMultiError(errors)
	}

	return nil
}

// TaskInfoMultiError is an error wrapping multiple validation errors returned
// by TaskInfo.ValidateAll() if the designated constraints aren't met.
type TaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskInfoMultiError) AllErrors() []error { return m }

// TaskInfoValidationError is the validation error returned by
// TaskInfo.Validate if the designated constraints aren't met.
type TaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskInfoValidationError) ErrorName() string { return "TaskInfoValidationError" }

// Error satisfies the builtin error interface
func (e TaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskInfoValidationError{}

// Validate checks the field values on DescribeTaskReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DescribeTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeTaskReqMultiError, or nil if none found.
func (m *DescribeTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskID

	if len(errors) > 0 {
		return DescribeTaskReqMultiError(errors)
	}

	return nil
}

// DescribeTaskReqMultiError is an error wrapping multiple validation errors
// returned by DescribeTaskReq.ValidateAll() if the designated constraints
// aren't met.
type DescribeTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeTaskReqMultiError) AllErrors() []error { return m }

// DescribeTaskReqValidationError is the validation error returned by
// DescribeTaskReq.Validate if the designated constraints aren't met.
type DescribeTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeTaskReqValidationError) ErrorName() string { return "DescribeTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e DescribeTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeTaskReqValidationError{}

// Validate checks the field values on PushTaskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushTaskInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PushTaskInfoMultiError, or
// nil if none found.
func (m *PushTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PushTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CreateTime

	// no validation rules for Message

	// no validation rules for Retry

	// no validation rules for State

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return PushTaskInfoMultiError(errors)
	}

	return nil
}

// PushTaskInfoMultiError is an error wrapping multiple validation errors
// returned by PushTaskInfo.ValidateAll() if the designated constraints aren't met.
type PushTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushTaskInfoMultiError) AllErrors() []error { return m }

// PushTaskInfoValidationError is the validation error returned by
// PushTaskInfo.Validate if the designated constraints aren't met.
type PushTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushTaskInfoValidationError) ErrorName() string { return "PushTaskInfoValidationError" }

// Error satisfies the builtin error interface
func (e PushTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushTaskInfoValidationError{}

// Validate checks the field values on DescribeCommonFileTaskReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeCommonFileTaskReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeCommonFileTaskReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeCommonFileTaskReplyMultiError, or nil if none found.
func (m *DescribeCommonFileTaskReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeCommonFileTaskReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeCommonFileTaskReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeCommonFileTaskReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeCommonFileTaskReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if len(errors) > 0 {
		return DescribeCommonFileTaskReplyMultiError(errors)
	}

	return nil
}

// DescribeCommonFileTaskReplyMultiError is an error wrapping multiple
// validation errors returned by DescribeCommonFileTaskReply.ValidateAll() if
// the designated constraints aren't met.
type DescribeCommonFileTaskReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeCommonFileTaskReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeCommonFileTaskReplyMultiError) AllErrors() []error { return m }

// DescribeCommonFileTaskReplyValidationError is the validation error returned
// by DescribeCommonFileTaskReply.Validate if the designated constraints
// aren't met.
type DescribeCommonFileTaskReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeCommonFileTaskReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeCommonFileTaskReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeCommonFileTaskReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeCommonFileTaskReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeCommonFileTaskReplyValidationError) ErrorName() string {
	return "DescribeCommonFileTaskReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeCommonFileTaskReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeCommonFileTaskReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeCommonFileTaskReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeCommonFileTaskReplyValidationError{}

// Validate checks the field values on DescribesArtifactReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesArtifactReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesArtifactReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesArtifactReqMultiError, or nil if none found.
func (m *DescribesArtifactReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesArtifactReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceCode

	// no validation rules for Version

	if _, ok := _DescribesArtifactReq_Architecture_InLookup[m.GetArchitecture()]; !ok {
		err := DescribesArtifactReqValidationError{
			field:  "Architecture",
			reason: "value must be in list [x86_64 arm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHostOSType() != "" {

		if _, ok := _DescribesArtifactReq_HostOSType_InLookup[m.GetHostOSType()]; !ok {
			err := DescribesArtifactReqValidationError{
				field:  "HostOSType",
				reason: "value must be in list [centos openEuler kylin]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for AppName

	if len(errors) > 0 {
		return DescribesArtifactReqMultiError(errors)
	}

	return nil
}

// DescribesArtifactReqMultiError is an error wrapping multiple validation
// errors returned by DescribesArtifactReq.ValidateAll() if the designated
// constraints aren't met.
type DescribesArtifactReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesArtifactReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesArtifactReqMultiError) AllErrors() []error { return m }

// DescribesArtifactReqValidationError is the validation error returned by
// DescribesArtifactReq.Validate if the designated constraints aren't met.
type DescribesArtifactReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesArtifactReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesArtifactReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesArtifactReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesArtifactReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesArtifactReqValidationError) ErrorName() string {
	return "DescribesArtifactReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesArtifactReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesArtifactReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesArtifactReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesArtifactReqValidationError{}

var _DescribesArtifactReq_Architecture_InLookup = map[string]struct{}{
	"x86_64": {},
	"arm":    {},
}

var _DescribesArtifactReq_HostOSType_InLookup = map[string]struct{}{
	"centos":    {},
	"openEuler": {},
	"kylin":     {},
}

// Validate checks the field values on DescribesArtifactReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribesArtifactReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribesArtifactReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribesArtifactReplyMultiError, or nil if none found.
func (m *DescribesArtifactReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribesArtifactReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRegularFile() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("RegularFile[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("RegularFile[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesArtifactReplyValidationError{
					field:  fmt.Sprintf("RegularFile[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRpm() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("Rpm[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("Rpm[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesArtifactReplyValidationError{
					field:  fmt.Sprintf("Rpm[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSkywingImage() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("SkywingImage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("SkywingImage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesArtifactReplyValidationError{
					field:  fmt.Sprintf("SkywingImage[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSkywingPackage() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("SkywingPackage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("SkywingPackage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesArtifactReplyValidationError{
					field:  fmt.Sprintf("SkywingPackage[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetXingyunImage() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("XingyunImage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("XingyunImage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesArtifactReplyValidationError{
					field:  fmt.Sprintf("XingyunImage[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetXingyunPackage() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("XingyunPackage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribesArtifactReplyValidationError{
						field:  fmt.Sprintf("XingyunPackage[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribesArtifactReplyValidationError{
					field:  fmt.Sprintf("XingyunPackage[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribesArtifactReplyMultiError(errors)
	}

	return nil
}

// DescribesArtifactReplyMultiError is an error wrapping multiple validation
// errors returned by DescribesArtifactReply.ValidateAll() if the designated
// constraints aren't met.
type DescribesArtifactReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribesArtifactReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribesArtifactReplyMultiError) AllErrors() []error { return m }

// DescribesArtifactReplyValidationError is the validation error returned by
// DescribesArtifactReply.Validate if the designated constraints aren't met.
type DescribesArtifactReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribesArtifactReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribesArtifactReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribesArtifactReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribesArtifactReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribesArtifactReplyValidationError) ErrorName() string {
	return "DescribesArtifactReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribesArtifactReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribesArtifactReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribesArtifactReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribesArtifactReplyValidationError{}

// Validate checks the field values on AppArtifact with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppArtifact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppArtifact with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AppArtifactMultiError, or
// nil if none found.
func (m *AppArtifact) ValidateAll() error {
	return m.validate(true)
}

func (m *AppArtifact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppName

	// no validation rules for AppServiceCode

	// no validation rules for AppVersion

	// no validation rules for FileIdentifier

	// no validation rules for HostOsType

	if len(errors) > 0 {
		return AppArtifactMultiError(errors)
	}

	return nil
}

// AppArtifactMultiError is an error wrapping multiple validation errors
// returned by AppArtifact.ValidateAll() if the designated constraints aren't met.
type AppArtifactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppArtifactMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppArtifactMultiError) AllErrors() []error { return m }

// AppArtifactValidationError is the validation error returned by
// AppArtifact.Validate if the designated constraints aren't met.
type AppArtifactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppArtifactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppArtifactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppArtifactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppArtifactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppArtifactValidationError) ErrorName() string { return "AppArtifactValidationError" }

// Error satisfies the builtin error interface
func (e AppArtifactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppArtifact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppArtifactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppArtifactValidationError{}

// Validate checks the field values on CreateArtifactPackageReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateArtifactPackageReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateArtifactPackageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateArtifactPackageReqMultiError, or nil if none found.
func (m *CreateArtifactPackageReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateArtifactPackageReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Architecture

	// no validation rules for BaseVersion

	// no validation rules for DestHostIp

	// no validation rules for DestPath

	// no validation rules for DestSshPort

	for idx, item := range m.GetHostOsTypes() {
		_, _ = idx, item

		if _, ok := _CreateArtifactPackageReq_HostOsTypes_InLookup[item]; !ok {
			err := CreateArtifactPackageReqValidationError{
				field:  fmt.Sprintf("HostOsTypes[%v]", idx),
				reason: "value must be in list [centos openeuler22 kylinv10]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for PackageVersion

	// no validation rules for PackageVersionType

	// no validation rules for SnapshotGeneratedTimeStamp

	// no validation rules for TaskType

	// no validation rules for JdstackReleaseS3

	// no validation rules for ClearArtifactDirBeforePackage

	if len(errors) > 0 {
		return CreateArtifactPackageReqMultiError(errors)
	}

	return nil
}

// CreateArtifactPackageReqMultiError is an error wrapping multiple validation
// errors returned by CreateArtifactPackageReq.ValidateAll() if the designated
// constraints aren't met.
type CreateArtifactPackageReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateArtifactPackageReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateArtifactPackageReqMultiError) AllErrors() []error { return m }

// CreateArtifactPackageReqValidationError is the validation error returned by
// CreateArtifactPackageReq.Validate if the designated constraints aren't met.
type CreateArtifactPackageReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateArtifactPackageReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateArtifactPackageReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateArtifactPackageReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateArtifactPackageReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateArtifactPackageReqValidationError) ErrorName() string {
	return "CreateArtifactPackageReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateArtifactPackageReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateArtifactPackageReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateArtifactPackageReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateArtifactPackageReqValidationError{}

var _CreateArtifactPackageReq_HostOsTypes_InLookup = map[string]struct{}{
	"centos":      {},
	"openeuler22": {},
	"kylinv10":    {},
}

// Validate checks the field values on DescribeArtifactPackageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeArtifactPackageReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeArtifactPackageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeArtifactPackageReqMultiError, or nil if none found.
func (m *DescribeArtifactPackageReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeArtifactPackageReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PackageVersion

	// no validation rules for Architecture

	// no validation rules for HostOsType

	if len(errors) > 0 {
		return DescribeArtifactPackageReqMultiError(errors)
	}

	return nil
}

// DescribeArtifactPackageReqMultiError is an error wrapping multiple
// validation errors returned by DescribeArtifactPackageReq.ValidateAll() if
// the designated constraints aren't met.
type DescribeArtifactPackageReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeArtifactPackageReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeArtifactPackageReqMultiError) AllErrors() []error { return m }

// DescribeArtifactPackageReqValidationError is the validation error returned
// by DescribeArtifactPackageReq.Validate if the designated constraints aren't met.
type DescribeArtifactPackageReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeArtifactPackageReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeArtifactPackageReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeArtifactPackageReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeArtifactPackageReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeArtifactPackageReqValidationError) ErrorName() string {
	return "DescribeArtifactPackageReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeArtifactPackageReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeArtifactPackageReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeArtifactPackageReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeArtifactPackageReqValidationError{}

// Validate checks the field values on DescribeArtifactPackageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeArtifactPackageReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeArtifactPackageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeArtifactPackageReplyMultiError, or nil if none found.
func (m *DescribeArtifactPackageReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeArtifactPackageReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for ErrorMsg

	// no validation rules for Log

	if len(errors) > 0 {
		return DescribeArtifactPackageReplyMultiError(errors)
	}

	return nil
}

// DescribeArtifactPackageReplyMultiError is an error wrapping multiple
// validation errors returned by DescribeArtifactPackageReply.ValidateAll() if
// the designated constraints aren't met.
type DescribeArtifactPackageReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeArtifactPackageReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeArtifactPackageReplyMultiError) AllErrors() []error { return m }

// DescribeArtifactPackageReplyValidationError is the validation error returned
// by DescribeArtifactPackageReply.Validate if the designated constraints
// aren't met.
type DescribeArtifactPackageReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeArtifactPackageReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeArtifactPackageReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeArtifactPackageReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeArtifactPackageReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeArtifactPackageReplyValidationError) ErrorName() string {
	return "DescribeArtifactPackageReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeArtifactPackageReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeArtifactPackageReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeArtifactPackageReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeArtifactPackageReplyValidationError{}

// Validate checks the field values on CreateMetadataPackageReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMetadataPackageReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMetadataPackageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMetadataPackageReqMultiError, or nil if none found.
func (m *CreateMetadataPackageReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMetadataPackageReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BaseVersion

	// no validation rules for DestHostIp

	// no validation rules for DestPath

	// no validation rules for DestSshPort

	// no validation rules for PackageVersion

	// no validation rules for TaskType

	if len(errors) > 0 {
		return CreateMetadataPackageReqMultiError(errors)
	}

	return nil
}

// CreateMetadataPackageReqMultiError is an error wrapping multiple validation
// errors returned by CreateMetadataPackageReq.ValidateAll() if the designated
// constraints aren't met.
type CreateMetadataPackageReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMetadataPackageReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMetadataPackageReqMultiError) AllErrors() []error { return m }

// CreateMetadataPackageReqValidationError is the validation error returned by
// CreateMetadataPackageReq.Validate if the designated constraints aren't met.
type CreateMetadataPackageReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMetadataPackageReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMetadataPackageReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMetadataPackageReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMetadataPackageReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMetadataPackageReqValidationError) ErrorName() string {
	return "CreateMetadataPackageReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMetadataPackageReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMetadataPackageReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMetadataPackageReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMetadataPackageReqValidationError{}

// Validate checks the field values on PackageTaskInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PackageTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PackageTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PackageTaskInfoMultiError, or nil if none found.
func (m *PackageTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PackageTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetDestHostIp()); l < 0 || l > 64 {
		err := PackageTaskInfoValidationError{
			field:  "DestHostIp",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetDestPath()); l < 0 || l > 64 {
		err := PackageTaskInfoValidationError{
			field:  "DestPath",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPackageVersion()); l < 0 || l > 64 {
		err := PackageTaskInfoValidationError{
			field:  "PackageVersion",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DestSshPort

	if l := utf8.RuneCountInString(m.GetState()); l < 0 || l > 128 {
		err := PackageTaskInfoValidationError{
			field:  "State",
			reason: "value length must be between 0 and 128 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetMessage()); l < 0 || l > 128 {
		err := PackageTaskInfoValidationError{
			field:  "Message",
			reason: "value length must be between 0 and 128 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetArchitecture()); l < 0 || l > 64 {
		err := PackageTaskInfoValidationError{
			field:  "Architecture",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetHostOsType()); l < 0 || l > 64 {
		err := PackageTaskInfoValidationError{
			field:  "HostOsType",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CreateTime

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return PackageTaskInfoMultiError(errors)
	}

	return nil
}

// PackageTaskInfoMultiError is an error wrapping multiple validation errors
// returned by PackageTaskInfo.ValidateAll() if the designated constraints
// aren't met.
type PackageTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PackageTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PackageTaskInfoMultiError) AllErrors() []error { return m }

// PackageTaskInfoValidationError is the validation error returned by
// PackageTaskInfo.Validate if the designated constraints aren't met.
type PackageTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PackageTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PackageTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PackageTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PackageTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PackageTaskInfoValidationError) ErrorName() string { return "PackageTaskInfoValidationError" }

// Error satisfies the builtin error interface
func (e PackageTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPackageTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PackageTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PackageTaskInfoValidationError{}

// Validate checks the field values on DescribeMetadataPackageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeMetadataPackageReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeMetadataPackageReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeMetadataPackageReqMultiError, or nil if none found.
func (m *DescribeMetadataPackageReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeMetadataPackageReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PackageVersion

	// no validation rules for CreateTime

	if len(errors) > 0 {
		return DescribeMetadataPackageReqMultiError(errors)
	}

	return nil
}

// DescribeMetadataPackageReqMultiError is an error wrapping multiple
// validation errors returned by DescribeMetadataPackageReq.ValidateAll() if
// the designated constraints aren't met.
type DescribeMetadataPackageReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeMetadataPackageReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeMetadataPackageReqMultiError) AllErrors() []error { return m }

// DescribeMetadataPackageReqValidationError is the validation error returned
// by DescribeMetadataPackageReq.Validate if the designated constraints aren't met.
type DescribeMetadataPackageReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeMetadataPackageReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeMetadataPackageReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeMetadataPackageReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeMetadataPackageReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeMetadataPackageReqValidationError) ErrorName() string {
	return "DescribeMetadataPackageReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeMetadataPackageReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeMetadataPackageReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeMetadataPackageReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeMetadataPackageReqValidationError{}

// Validate checks the field values on DescribeSCFileTaskReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeSCFileTaskReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeSCFileTaskReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeSCFileTaskReplyMultiError, or nil if none found.
func (m *DescribeSCFileTaskReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeSCFileTaskReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeSCFileTaskReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeSCFileTaskReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeSCFileTaskReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetMessage()); l < 0 || l > 1024 {
		err := DescribeSCFileTaskReplyValidationError{
			field:  "Message",
			reason: "value length must be between 0 and 1024 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DescribeSCFileTaskReplyMultiError(errors)
	}

	return nil
}

// DescribeSCFileTaskReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeSCFileTaskReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeSCFileTaskReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeSCFileTaskReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeSCFileTaskReplyMultiError) AllErrors() []error { return m }

// DescribeSCFileTaskReplyValidationError is the validation error returned by
// DescribeSCFileTaskReply.Validate if the designated constraints aren't met.
type DescribeSCFileTaskReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeSCFileTaskReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeSCFileTaskReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeSCFileTaskReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeSCFileTaskReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeSCFileTaskReplyValidationError) ErrorName() string {
	return "DescribeSCFileTaskReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeSCFileTaskReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeSCFileTaskReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeSCFileTaskReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeSCFileTaskReplyValidationError{}

// Validate checks the field values on DescribeSCFilesResponseData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeSCFilesResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeSCFilesResponseData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeSCFilesResponseDataMultiError, or nil if none found.
func (m *DescribeSCFilesResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeSCFilesResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetAppName()); l < 0 || l > 64 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "AppName",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetArchitecture()); l < 0 || l > 64 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "Architecture",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CreateTime

	if l := utf8.RuneCountInString(m.GetHostOsType()); l < 0 || l > 64 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "HostOsType",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetMessage()); l < 0 || l > 128 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "Message",
			reason: "value length must be between 0 and 128 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetRegion()); l < 0 || l > 64 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "Region",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetServiceCode()); l < 0 || l > 64 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "ServiceCode",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetState()); l < 0 || l > 128 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "State",
			reason: "value length must be between 0 and 128 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UpdateTime

	if l := utf8.RuneCountInString(m.GetVersion()); l < 0 || l > 64 {
		err := DescribeSCFilesResponseDataValidationError{
			field:  "Version",
			reason: "value length must be between 0 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DescribeSCFilesResponseDataMultiError(errors)
	}

	return nil
}

// DescribeSCFilesResponseDataMultiError is an error wrapping multiple
// validation errors returned by DescribeSCFilesResponseData.ValidateAll() if
// the designated constraints aren't met.
type DescribeSCFilesResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeSCFilesResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeSCFilesResponseDataMultiError) AllErrors() []error { return m }

// DescribeSCFilesResponseDataValidationError is the validation error returned
// by DescribeSCFilesResponseData.Validate if the designated constraints
// aren't met.
type DescribeSCFilesResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeSCFilesResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeSCFilesResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeSCFilesResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeSCFilesResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeSCFilesResponseDataValidationError) ErrorName() string {
	return "DescribeSCFilesResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeSCFilesResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeSCFilesResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeSCFilesResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeSCFilesResponseDataValidationError{}

// Validate checks the field values on CreateSCFilesTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSCFilesTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSCFilesTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSCFilesTaskReqMultiError, or nil if none found.
func (m *CreateSCFilesTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSCFilesTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetServiceCode()); l < 1 || l > 64 {
		err := CreateSCFilesTaskReqValidationError{
			field:  "ServiceCode",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetVersion()); l < 1 || l > 64 {
		err := CreateSCFilesTaskReqValidationError{
			field:  "Version",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetRegion()); l < 1 || l > 64 {
		err := CreateSCFilesTaskReqValidationError{
			field:  "Region",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetArchitecture()); l < 1 || l > 64 {
		err := CreateSCFilesTaskReqValidationError{
			field:  "Architecture",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHostOsType() != "" {

		if _, ok := _CreateSCFilesTaskReq_HostOsType_InLookup[m.GetHostOsType()]; !ok {
			err := CreateSCFilesTaskReqValidationError{
				field:  "HostOsType",
				reason: "value must be in list [centos openEuler kylin]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for AppName

	if len(errors) > 0 {
		return CreateSCFilesTaskReqMultiError(errors)
	}

	return nil
}

// CreateSCFilesTaskReqMultiError is an error wrapping multiple validation
// errors returned by CreateSCFilesTaskReq.ValidateAll() if the designated
// constraints aren't met.
type CreateSCFilesTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSCFilesTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSCFilesTaskReqMultiError) AllErrors() []error { return m }

// CreateSCFilesTaskReqValidationError is the validation error returned by
// CreateSCFilesTaskReq.Validate if the designated constraints aren't met.
type CreateSCFilesTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSCFilesTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSCFilesTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSCFilesTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSCFilesTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSCFilesTaskReqValidationError) ErrorName() string {
	return "CreateSCFilesTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSCFilesTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSCFilesTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSCFilesTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSCFilesTaskReqValidationError{}

var _CreateSCFilesTaskReq_HostOsType_InLookup = map[string]struct{}{
	"centos":    {},
	"openEuler": {},
	"kylin":     {},
}

// Validate checks the field values on AppFilesTaskReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AppFilesTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppFilesTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppFilesTaskReqMultiError, or nil if none found.
func (m *AppFilesTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AppFilesTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetVersion()); l < 1 || l > 64 {
		err := AppFilesTaskReqValidationError{
			field:  "Version",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetServiceCode()) < 1 {
		err := AppFilesTaskReqValidationError{
			field:  "ServiceCode",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetProCode()) < 1 {
		err := AppFilesTaskReqValidationError{
			field:  "ProCode",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AppCode

	// no validation rules for AppVersionID

	if _, ok := _AppFilesTaskReq_DeployMode_InLookup[m.GetDeployMode()]; !ok {
		err := AppFilesTaskReqValidationError{
			field:  "DeployMode",
			reason: "value must be in list [skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EnvName

	// no validation rules for Region

	for idx, item := range m.GetArtifactInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppFilesTaskReqValidationError{
						field:  fmt.Sprintf("ArtifactInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppFilesTaskReqValidationError{
						field:  fmt.Sprintf("ArtifactInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppFilesTaskReqValidationError{
					field:  fmt.Sprintf("ArtifactInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AppFilesTaskReqMultiError(errors)
	}

	return nil
}

// AppFilesTaskReqMultiError is an error wrapping multiple validation errors
// returned by AppFilesTaskReq.ValidateAll() if the designated constraints
// aren't met.
type AppFilesTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppFilesTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppFilesTaskReqMultiError) AllErrors() []error { return m }

// AppFilesTaskReqValidationError is the validation error returned by
// AppFilesTaskReq.Validate if the designated constraints aren't met.
type AppFilesTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppFilesTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppFilesTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppFilesTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppFilesTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppFilesTaskReqValidationError) ErrorName() string { return "AppFilesTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e AppFilesTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppFilesTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppFilesTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppFilesTaskReqValidationError{}

var _AppFilesTaskReq_DeployMode_InLookup = map[string]struct{}{
	"skywing_container": {},
	"skywing_package":   {},
	"mate_container":    {},
	"shell":             {},
	"helm":              {},
	"rpm":               {},
	"mate_bin":          {},
	"mate_v2":           {},
	"iaas_image":        {},
	"xingyun_container": {},
	"xingyun_package":   {},
}

// Validate checks the field values on PushFileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushFileInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PushFileInfoMultiError, or
// nil if none found.
func (m *PushFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PushFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _PushFileInfo_FileType_InLookup[m.GetFileType()]; !ok {
		err := PushFileInfoValidationError{
			field:  "FileType",
			reason: "value must be in list [image out_image rpm os_file package helm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FileName

	// no validation rules for Md5

	if _, ok := _PushFileInfo_Arch_InLookup[m.GetArch()]; !ok {
		err := PushFileInfoValidationError{
			field:  "Arch",
			reason: "value must be in list [amd64 arm64]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PushFileInfo_Os_InLookup[m.GetOs()]; !ok {
		err := PushFileInfoValidationError{
			field:  "Os",
			reason: "value must be in list [centos openeuler22 kylinv10]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FileStorageMode

	// no validation rules for FileBucket

	// no validation rules for RpmRepo

	// no validation rules for ImageRepo

	// no validation rules for ImageTag

	// no validation rules for PkgModelName

	// no validation rules for PkgVersion

	// no validation rules for Identity

	// no validation rules for Version

	if len(errors) > 0 {
		return PushFileInfoMultiError(errors)
	}

	return nil
}

// PushFileInfoMultiError is an error wrapping multiple validation errors
// returned by PushFileInfo.ValidateAll() if the designated constraints aren't met.
type PushFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushFileInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushFileInfoMultiError) AllErrors() []error { return m }

// PushFileInfoValidationError is the validation error returned by
// PushFileInfo.Validate if the designated constraints aren't met.
type PushFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushFileInfoValidationError) ErrorName() string { return "PushFileInfoValidationError" }

// Error satisfies the builtin error interface
func (e PushFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushFileInfoValidationError{}

var _PushFileInfo_FileType_InLookup = map[string]struct{}{
	"image":     {},
	"out_image": {},
	"rpm":       {},
	"os_file":   {},
	"package":   {},
	"helm":      {},
}

var _PushFileInfo_Arch_InLookup = map[string]struct{}{
	"amd64": {},
	"arm64": {},
}

var _PushFileInfo_Os_InLookup = map[string]struct{}{
	"centos":      {},
	"openeuler22": {},
	"kylinv10":    {},
}

// Validate checks the field values on DescribeAppFileTaskReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAppFileTaskReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAppFileTaskReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAppFileTaskReplyMultiError, or nil if none found.
func (m *DescribeAppFileTaskReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAppFileTaskReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for ErrorMsg

	// no validation rules for Log

	if len(errors) > 0 {
		return DescribeAppFileTaskReplyMultiError(errors)
	}

	return nil
}

// DescribeAppFileTaskReplyMultiError is an error wrapping multiple validation
// errors returned by DescribeAppFileTaskReply.ValidateAll() if the designated
// constraints aren't met.
type DescribeAppFileTaskReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAppFileTaskReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAppFileTaskReplyMultiError) AllErrors() []error { return m }

// DescribeAppFileTaskReplyValidationError is the validation error returned by
// DescribeAppFileTaskReply.Validate if the designated constraints aren't met.
type DescribeAppFileTaskReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAppFileTaskReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAppFileTaskReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAppFileTaskReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAppFileTaskReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAppFileTaskReplyValidationError) ErrorName() string {
	return "DescribeAppFileTaskReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAppFileTaskReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAppFileTaskReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAppFileTaskReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAppFileTaskReplyValidationError{}

// Validate checks the field values on CreateArtifactReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateArtifactReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateArtifactReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateArtifactReqMultiError, or nil if none found.
func (m *CreateArtifactReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateArtifactReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetVersion()); l < 1 || l > 64 {
		err := CreateArtifactReqValidationError{
			field:  "Version",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AppCode

	if _, ok := _CreateArtifactReq_Arch_InLookup[m.GetArch()]; !ok {
		err := CreateArtifactReqValidationError{
			field:  "Arch",
			reason: "value must be in list [x86_64 arm]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateArtifactReq_HostOsType_InLookup[m.GetHostOsType()]; !ok {
		err := CreateArtifactReqValidationError{
			field:  "HostOsType",
			reason: "value must be in list [centos openEuler kylin]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FileIdentifier

	if _, ok := _CreateArtifactReq_DirectorySpec_InLookup[m.GetDirectorySpec()]; !ok {
		err := CreateArtifactReqValidationError{
			field:  "DirectorySpec",
			reason: "value must be in list [DirectorySpecArchFileTypeServiceCode DirectorySpecArchSerializeGoods]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Filename

	// no validation rules for FileHash

	// no validation rules for FileSize

	// no validation rules for ImageTags

	// no validation rules for FileVersion

	// no validation rules for Param

	if len(errors) > 0 {
		return CreateArtifactReqMultiError(errors)
	}

	return nil
}

// CreateArtifactReqMultiError is an error wrapping multiple validation errors
// returned by CreateArtifactReq.ValidateAll() if the designated constraints
// aren't met.
type CreateArtifactReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateArtifactReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateArtifactReqMultiError) AllErrors() []error { return m }

// CreateArtifactReqValidationError is the validation error returned by
// CreateArtifactReq.Validate if the designated constraints aren't met.
type CreateArtifactReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateArtifactReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateArtifactReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateArtifactReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateArtifactReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateArtifactReqValidationError) ErrorName() string {
	return "CreateArtifactReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateArtifactReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateArtifactReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateArtifactReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateArtifactReqValidationError{}

var _CreateArtifactReq_Arch_InLookup = map[string]struct{}{
	"x86_64": {},
	"arm":    {},
}

var _CreateArtifactReq_HostOsType_InLookup = map[string]struct{}{
	"centos":    {},
	"openEuler": {},
	"kylin":     {},
}

var _CreateArtifactReq_DirectorySpec_InLookup = map[string]struct{}{
	"DirectorySpecArchFileTypeServiceCode": {},
	"DirectorySpecArchSerializeGoods":      {},
}

// Validate checks the field values on DeploymentServerInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeploymentServerInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeploymentServerInfoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeploymentServerInfoReplyMultiError, or nil if none found.
func (m *DeploymentServerInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeploymentServerInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for Path

	if len(errors) > 0 {
		return DeploymentServerInfoReplyMultiError(errors)
	}

	return nil
}

// DeploymentServerInfoReplyMultiError is an error wrapping multiple validation
// errors returned by DeploymentServerInfoReply.ValidateAll() if the
// designated constraints aren't met.
type DeploymentServerInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeploymentServerInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeploymentServerInfoReplyMultiError) AllErrors() []error { return m }

// DeploymentServerInfoReplyValidationError is the validation error returned by
// DeploymentServerInfoReply.Validate if the designated constraints aren't met.
type DeploymentServerInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeploymentServerInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeploymentServerInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeploymentServerInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeploymentServerInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeploymentServerInfoReplyValidationError) ErrorName() string {
	return "DeploymentServerInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeploymentServerInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeploymentServerInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeploymentServerInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeploymentServerInfoReplyValidationError{}
