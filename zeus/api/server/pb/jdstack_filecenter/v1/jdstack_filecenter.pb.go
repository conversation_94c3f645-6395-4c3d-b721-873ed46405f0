// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.21.1
// source: jdstack_filecenter.proto

package pbJdstackFilecenter

import (
	v1 "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/goods_stack/v1"
	v11 "coding.jd.com/fabric/zeusV2/zeus/api/server/pb/unified_metadata/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	empty "github.com/golang/protobuf/ptypes/empty"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FilePropertiesRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifier    string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	DeployMode    string `protobuf:"bytes,2,opt,name=deployMode,proto3" json:"deployMode,omitempty"`
	Filename      string `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`
	Address       string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	ModuleName    string `protobuf:"bytes,5,opt,name=moduleName,proto3" json:"moduleName,omitempty"`
	ImageTags     string `protobuf:"bytes,6,opt,name=imageTags,proto3" json:"imageTags,omitempty"`
	StorageMode   string `protobuf:"bytes,7,opt,name=storageMode,proto3" json:"storageMode,omitempty"`
	StorageBucket string `protobuf:"bytes,8,opt,name=storageBucket,proto3" json:"storageBucket,omitempty"`
	RepoKind      string `protobuf:"bytes,9,opt,name=repoKind,proto3" json:"repoKind,omitempty"`
	CommitId      string `protobuf:"bytes,10,opt,name=commitId,proto3" json:"commitId,omitempty"`
	Os            string `protobuf:"bytes,11,opt,name=os,proto3" json:"os,omitempty"`
	Arch          string `protobuf:"bytes,12,opt,name=arch,proto3" json:"arch,omitempty"`
}

func (x *FilePropertiesRes) Reset() {
	*x = FilePropertiesRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilePropertiesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilePropertiesRes) ProtoMessage() {}

func (x *FilePropertiesRes) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilePropertiesRes.ProtoReflect.Descriptor instead.
func (*FilePropertiesRes) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{0}
}

func (x *FilePropertiesRes) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *FilePropertiesRes) GetDeployMode() string {
	if x != nil {
		return x.DeployMode
	}
	return ""
}

func (x *FilePropertiesRes) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *FilePropertiesRes) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *FilePropertiesRes) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *FilePropertiesRes) GetImageTags() string {
	if x != nil {
		return x.ImageTags
	}
	return ""
}

func (x *FilePropertiesRes) GetStorageMode() string {
	if x != nil {
		return x.StorageMode
	}
	return ""
}

func (x *FilePropertiesRes) GetStorageBucket() string {
	if x != nil {
		return x.StorageBucket
	}
	return ""
}

func (x *FilePropertiesRes) GetRepoKind() string {
	if x != nil {
		return x.RepoKind
	}
	return ""
}

func (x *FilePropertiesRes) GetCommitId() string {
	if x != nil {
		return x.CommitId
	}
	return ""
}

func (x *FilePropertiesRes) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *FilePropertiesRes) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

type CreateUploadTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version     string               `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Policy      []string             `protobuf:"bytes,2,rep,name=policy,proto3" json:"policy,omitempty"`
	IsStore     bool                 `protobuf:"varint,3,opt,name=isStore,proto3" json:"isStore,omitempty"`
	ServiceCode string               `protobuf:"bytes,4,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	AppName     string               `protobuf:"bytes,5,opt,name=appName,proto3" json:"appName,omitempty"`
	Arch        string               `protobuf:"bytes,6,opt,name=arch,proto3" json:"arch,omitempty"`
	Os          string               `protobuf:"bytes,7,opt,name=os,proto3" json:"os,omitempty"`
	Files       []*FilePropertiesRes `protobuf:"bytes,8,rep,name=files,proto3" json:"files,omitempty"`
	EnvName     string               `protobuf:"bytes,9,opt,name=envName,proto3" json:"envName,omitempty"`
}

func (x *CreateUploadTaskReq) Reset() {
	*x = CreateUploadTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUploadTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUploadTaskReq) ProtoMessage() {}

func (x *CreateUploadTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUploadTaskReq.ProtoReflect.Descriptor instead.
func (*CreateUploadTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUploadTaskReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateUploadTaskReq) GetPolicy() []string {
	if x != nil {
		return x.Policy
	}
	return nil
}

func (x *CreateUploadTaskReq) GetIsStore() bool {
	if x != nil {
		return x.IsStore
	}
	return false
}

func (x *CreateUploadTaskReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *CreateUploadTaskReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *CreateUploadTaskReq) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *CreateUploadTaskReq) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *CreateUploadTaskReq) GetFiles() []*FilePropertiesRes {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *CreateUploadTaskReq) GetEnvName() string {
	if x != nil {
		return x.EnvName
	}
	return ""
}

type CreateFileDownloadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath     string `protobuf:"bytes,1,opt,name=filePath,proto3" json:"filePath,omitempty"`
	ServiceCode  string `protobuf:"bytes,2,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Arch         string `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
	FileType     string `protobuf:"bytes,4,opt,name=fileType,proto3" json:"fileType,omitempty"`
	AppVersionID string `protobuf:"bytes,5,opt,name=appVersionID,proto3" json:"appVersionID,omitempty"`
}

func (x *CreateFileDownloadReq) Reset() {
	*x = CreateFileDownloadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateFileDownloadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFileDownloadReq) ProtoMessage() {}

func (x *CreateFileDownloadReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFileDownloadReq.ProtoReflect.Descriptor instead.
func (*CreateFileDownloadReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{2}
}

func (x *CreateFileDownloadReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *CreateFileDownloadReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *CreateFileDownloadReq) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *CreateFileDownloadReq) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *CreateFileDownloadReq) GetAppVersionID() string {
	if x != nil {
		return x.AppVersionID
	}
	return ""
}

type DescribeFileDownloadReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FileName string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName,omitempty"`
	Md5      string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	ErrorMsg string `protobuf:"bytes,4,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
}

func (x *DescribeFileDownloadReply) Reset() {
	*x = DescribeFileDownloadReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeFileDownloadReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeFileDownloadReply) ProtoMessage() {}

func (x *DescribeFileDownloadReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeFileDownloadReply.ProtoReflect.Descriptor instead.
func (*DescribeFileDownloadReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeFileDownloadReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DescribeFileDownloadReply) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DescribeFileDownloadReply) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *DescribeFileDownloadReply) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type ModifyArtifactAndMetadataByDeployReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CloudType   string              `protobuf:"bytes,1,opt,name=cloudType,proto3" json:"cloudType,omitempty"`
	Version     string              `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Policy      []string            `protobuf:"bytes,3,rep,name=policy,proto3" json:"policy,omitempty"`
	ServiceCode string              `protobuf:"bytes,4,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Category    string              `protobuf:"bytes,5,opt,name=category,proto3" json:"category,omitempty"`
	ProductCode string              `protobuf:"bytes,6,opt,name=productCode,proto3" json:"productCode,omitempty"`
	ProductName string              `protobuf:"bytes,7,opt,name=productName,proto3" json:"productName,omitempty"`
	AppInfo     *ApplicationInfoReq `protobuf:"bytes,8,opt,name=appInfo,proto3" json:"appInfo,omitempty"`
}

func (x *ModifyArtifactAndMetadataByDeployReq) Reset() {
	*x = ModifyArtifactAndMetadataByDeployReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyArtifactAndMetadataByDeployReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyArtifactAndMetadataByDeployReq) ProtoMessage() {}

func (x *ModifyArtifactAndMetadataByDeployReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyArtifactAndMetadataByDeployReq.ProtoReflect.Descriptor instead.
func (*ModifyArtifactAndMetadataByDeployReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{4}
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetCloudType() string {
	if x != nil {
		return x.CloudType
	}
	return ""
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetPolicy() []string {
	if x != nil {
		return x.Policy
	}
	return nil
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *ModifyArtifactAndMetadataByDeployReq) GetAppInfo() *ApplicationInfoReq {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

type ModifyArtifactAndMetadataBySealReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CloudType        string                `protobuf:"bytes,1,opt,name=cloudType,proto3" json:"cloudType,omitempty"`
	Version          string                `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Policy           []string              `protobuf:"bytes,3,rep,name=policy,proto3" json:"policy,omitempty"`
	ServiceCode      string                `protobuf:"bytes,4,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Category         string                `protobuf:"bytes,5,opt,name=category,proto3" json:"category,omitempty"`
	ProductCode      string                `protobuf:"bytes,6,opt,name=productCode,proto3" json:"productCode,omitempty"`
	ProductName      string                `protobuf:"bytes,7,opt,name=productName,proto3" json:"productName,omitempty"`
	AppInfos         []*ApplicationInfoReq `protobuf:"bytes,8,rep,name=appInfos,proto3" json:"appInfos,omitempty"`
	ProductYaml      string                `protobuf:"bytes,9,opt,name=productYaml,proto3" json:"productYaml,omitempty"`
	AdlFiles         []*v1.AdlReq          `protobuf:"bytes,10,rep,name=adlFiles,proto3" json:"adlFiles,omitempty"`
	BusinessMetadata *v11.CreateReq        `protobuf:"bytes,11,opt,name=businessMetadata,proto3" json:"businessMetadata,omitempty"`
}

func (x *ModifyArtifactAndMetadataBySealReq) Reset() {
	*x = ModifyArtifactAndMetadataBySealReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyArtifactAndMetadataBySealReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyArtifactAndMetadataBySealReq) ProtoMessage() {}

func (x *ModifyArtifactAndMetadataBySealReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyArtifactAndMetadataBySealReq.ProtoReflect.Descriptor instead.
func (*ModifyArtifactAndMetadataBySealReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{5}
}

func (x *ModifyArtifactAndMetadataBySealReq) GetCloudType() string {
	if x != nil {
		return x.CloudType
	}
	return ""
}

func (x *ModifyArtifactAndMetadataBySealReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ModifyArtifactAndMetadataBySealReq) GetPolicy() []string {
	if x != nil {
		return x.Policy
	}
	return nil
}

func (x *ModifyArtifactAndMetadataBySealReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *ModifyArtifactAndMetadataBySealReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ModifyArtifactAndMetadataBySealReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *ModifyArtifactAndMetadataBySealReq) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *ModifyArtifactAndMetadataBySealReq) GetAppInfos() []*ApplicationInfoReq {
	if x != nil {
		return x.AppInfos
	}
	return nil
}

func (x *ModifyArtifactAndMetadataBySealReq) GetProductYaml() string {
	if x != nil {
		return x.ProductYaml
	}
	return ""
}

func (x *ModifyArtifactAndMetadataBySealReq) GetAdlFiles() []*v1.AdlReq {
	if x != nil {
		return x.AdlFiles
	}
	return nil
}

func (x *ModifyArtifactAndMetadataBySealReq) GetBusinessMetadata() *v11.CreateReq {
	if x != nil {
		return x.BusinessMetadata
	}
	return nil
}

type DescribeArtifactAndMetadataBySealReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *DescribeArtifactAndMetadataBySealReply) Reset() {
	*x = DescribeArtifactAndMetadataBySealReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeArtifactAndMetadataBySealReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeArtifactAndMetadataBySealReply) ProtoMessage() {}

func (x *DescribeArtifactAndMetadataBySealReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeArtifactAndMetadataBySealReply.ProtoReflect.Descriptor instead.
func (*DescribeArtifactAndMetadataBySealReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{6}
}

func (x *DescribeArtifactAndMetadataBySealReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DescribeArtifactAndMetadataBySealReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ApplicationInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppCode      string         `protobuf:"bytes,1,opt,name=appCode,proto3" json:"appCode,omitempty"`
	AppName      string         `protobuf:"bytes,2,opt,name=appName,proto3" json:"appName,omitempty"`
	DeployMode   string         `protobuf:"bytes,3,opt,name=deployMode,proto3" json:"deployMode,omitempty"`
	VirtualGoods string         `protobuf:"bytes,4,opt,name=virtualGoods,proto3" json:"virtualGoods,omitempty"`
	Artifacts    []*ArtifactReq `protobuf:"bytes,5,rep,name=artifacts,proto3" json:"artifacts,omitempty"`
	AppYaml      string         `protobuf:"bytes,6,opt,name=appYaml,proto3" json:"appYaml,omitempty"`
	AdlFiles     []*v1.AdlReq   `protobuf:"bytes,7,rep,name=adlFiles,proto3" json:"adlFiles,omitempty"`
}

func (x *ApplicationInfoReq) Reset() {
	*x = ApplicationInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationInfoReq) ProtoMessage() {}

func (x *ApplicationInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationInfoReq.ProtoReflect.Descriptor instead.
func (*ApplicationInfoReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{7}
}

func (x *ApplicationInfoReq) GetAppCode() string {
	if x != nil {
		return x.AppCode
	}
	return ""
}

func (x *ApplicationInfoReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ApplicationInfoReq) GetDeployMode() string {
	if x != nil {
		return x.DeployMode
	}
	return ""
}

func (x *ApplicationInfoReq) GetVirtualGoods() string {
	if x != nil {
		return x.VirtualGoods
	}
	return ""
}

func (x *ApplicationInfoReq) GetArtifacts() []*ArtifactReq {
	if x != nil {
		return x.Artifacts
	}
	return nil
}

func (x *ApplicationInfoReq) GetAppYaml() string {
	if x != nil {
		return x.AppYaml
	}
	return ""
}

func (x *ApplicationInfoReq) GetAdlFiles() []*v1.AdlReq {
	if x != nil {
		return x.AdlFiles
	}
	return nil
}

type ArtifactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identifier      string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	Version         string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	FileType        string `protobuf:"bytes,3,opt,name=fileType,proto3" json:"fileType,omitempty"`
	FileName        string `protobuf:"bytes,4,opt,name=fileName,proto3" json:"fileName,omitempty"`
	Md5             string `protobuf:"bytes,5,opt,name=md5,proto3" json:"md5,omitempty"`
	Address         string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	FileStorageMode string `protobuf:"bytes,7,opt,name=fileStorageMode,proto3" json:"fileStorageMode,omitempty"`
	FileBucket      string `protobuf:"bytes,8,opt,name=fileBucket,proto3" json:"fileBucket,omitempty"`
	RpmRepo         string `protobuf:"bytes,9,opt,name=rpmRepo,proto3" json:"rpmRepo,omitempty"`
	ImageRepo       string `protobuf:"bytes,10,opt,name=imageRepo,proto3" json:"imageRepo,omitempty"`
	ImageTag        string `protobuf:"bytes,11,opt,name=imageTag,proto3" json:"imageTag,omitempty"`
	PkgModelName    string `protobuf:"bytes,12,opt,name=pkgModelName,proto3" json:"pkgModelName,omitempty"`
	PkgVersion      string `protobuf:"bytes,13,opt,name=pkgVersion,proto3" json:"pkgVersion,omitempty"`
	Os              string `protobuf:"bytes,14,opt,name=os,proto3" json:"os,omitempty"`
	Arch            string `protobuf:"bytes,15,opt,name=arch,proto3" json:"arch,omitempty"`
}

func (x *ArtifactReq) Reset() {
	*x = ArtifactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArtifactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtifactReq) ProtoMessage() {}

func (x *ArtifactReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtifactReq.ProtoReflect.Descriptor instead.
func (*ArtifactReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{8}
}

func (x *ArtifactReq) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *ArtifactReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ArtifactReq) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *ArtifactReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ArtifactReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ArtifactReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ArtifactReq) GetFileStorageMode() string {
	if x != nil {
		return x.FileStorageMode
	}
	return ""
}

func (x *ArtifactReq) GetFileBucket() string {
	if x != nil {
		return x.FileBucket
	}
	return ""
}

func (x *ArtifactReq) GetRpmRepo() string {
	if x != nil {
		return x.RpmRepo
	}
	return ""
}

func (x *ArtifactReq) GetImageRepo() string {
	if x != nil {
		return x.ImageRepo
	}
	return ""
}

func (x *ArtifactReq) GetImageTag() string {
	if x != nil {
		return x.ImageTag
	}
	return ""
}

func (x *ArtifactReq) GetPkgModelName() string {
	if x != nil {
		return x.PkgModelName
	}
	return ""
}

func (x *ArtifactReq) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *ArtifactReq) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *ArtifactReq) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

type RegularFileTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName         string `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	Architecture    string `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	Description     string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	DestPath        string `protobuf:"bytes,4,opt,name=destPath,proto3" json:"destPath,omitempty"`
	FileIdentifier  string `protobuf:"bytes,5,opt,name=fileIdentifier,proto3" json:"fileIdentifier,omitempty"`
	Filename        string `protobuf:"bytes,6,opt,name=filename,proto3" json:"filename,omitempty"`
	HostOsType      string `protobuf:"bytes,7,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	Md5             string `protobuf:"bytes,8,opt,name=md5,proto3" json:"md5,omitempty"`
	Override        bool   `protobuf:"varint,9,opt,name=override,proto3" json:"override,omitempty"`
	Region          string `protobuf:"bytes,10,opt,name=region,proto3" json:"region,omitempty"`
	ServiceCode     string `protobuf:"bytes,11,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	StorageLocation string `protobuf:"bytes,12,opt,name=storageLocation,proto3" json:"storageLocation,omitempty"`
	Version         string `protobuf:"bytes,13,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *RegularFileTaskReq) Reset() {
	*x = RegularFileTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegularFileTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegularFileTaskReq) ProtoMessage() {}

func (x *RegularFileTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegularFileTaskReq.ProtoReflect.Descriptor instead.
func (*RegularFileTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{9}
}

func (x *RegularFileTaskReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *RegularFileTaskReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *RegularFileTaskReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RegularFileTaskReq) GetDestPath() string {
	if x != nil {
		return x.DestPath
	}
	return ""
}

func (x *RegularFileTaskReq) GetFileIdentifier() string {
	if x != nil {
		return x.FileIdentifier
	}
	return ""
}

func (x *RegularFileTaskReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *RegularFileTaskReq) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *RegularFileTaskReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *RegularFileTaskReq) GetOverride() bool {
	if x != nil {
		return x.Override
	}
	return false
}

func (x *RegularFileTaskReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *RegularFileTaskReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *RegularFileTaskReq) GetStorageLocation() string {
	if x != nil {
		return x.StorageLocation
	}
	return ""
}

func (x *RegularFileTaskReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type RpmTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName        string `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	Architecture   string `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	Description    string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	FileIdentifier string `protobuf:"bytes,4,opt,name=fileIdentifier,proto3" json:"fileIdentifier,omitempty"`
	Filename       string `protobuf:"bytes,5,opt,name=filename,proto3" json:"filename,omitempty"`
	HostOsType     string `protobuf:"bytes,6,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	Md5            string `protobuf:"bytes,7,opt,name=md5,proto3" json:"md5,omitempty"`
	Override       bool   `protobuf:"varint,8,opt,name=override,proto3" json:"override,omitempty"`
	Region         string `protobuf:"bytes,9,opt,name=region,proto3" json:"region,omitempty"`
	Repo           string `protobuf:"bytes,10,opt,name=repo,proto3" json:"repo,omitempty"`
	ServiceCode    string `protobuf:"bytes,11,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Version        string `protobuf:"bytes,12,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *RpmTaskReq) Reset() {
	*x = RpmTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpmTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpmTaskReq) ProtoMessage() {}

func (x *RpmTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpmTaskReq.ProtoReflect.Descriptor instead.
func (*RpmTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{10}
}

func (x *RpmTaskReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *RpmTaskReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *RpmTaskReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RpmTaskReq) GetFileIdentifier() string {
	if x != nil {
		return x.FileIdentifier
	}
	return ""
}

func (x *RpmTaskReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *RpmTaskReq) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *RpmTaskReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *RpmTaskReq) GetOverride() bool {
	if x != nil {
		return x.Override
	}
	return false
}

func (x *RpmTaskReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *RpmTaskReq) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *RpmTaskReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *RpmTaskReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type SkywingPackageTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName        string `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	Architecture   string `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	Description    string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Filename       string `protobuf:"bytes,4,opt,name=filename,proto3" json:"filename,omitempty"`
	HostOsType     string `protobuf:"bytes,5,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	Md5            string `protobuf:"bytes,6,opt,name=md5,proto3" json:"md5,omitempty"`
	ModuleName     string `protobuf:"bytes,7,opt,name=moduleName,proto3" json:"moduleName,omitempty"`
	Override       bool   `protobuf:"varint,8,opt,name=override,proto3" json:"override,omitempty"`
	PackageVersion string `protobuf:"bytes,9,opt,name=packageVersion,proto3" json:"packageVersion,omitempty"`
	Region         string `protobuf:"bytes,10,opt,name=region,proto3" json:"region,omitempty"`
	ServiceCode    string `protobuf:"bytes,11,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Version        string `protobuf:"bytes,12,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *SkywingPackageTaskReq) Reset() {
	*x = SkywingPackageTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkywingPackageTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkywingPackageTaskReq) ProtoMessage() {}

func (x *SkywingPackageTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkywingPackageTaskReq.ProtoReflect.Descriptor instead.
func (*SkywingPackageTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{11}
}

func (x *SkywingPackageTaskReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetOverride() bool {
	if x != nil {
		return x.Override
	}
	return false
}

func (x *SkywingPackageTaskReq) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *SkywingPackageTaskReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DockerImageTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName        string `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	Architecture   string `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	Description    string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	FileIdentifier string `protobuf:"bytes,4,opt,name=fileIdentifier,proto3" json:"fileIdentifier,omitempty"`
	Filename       string `protobuf:"bytes,5,opt,name=filename,proto3" json:"filename,omitempty"`
	HostOsType     string `protobuf:"bytes,6,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	Md5            string `protobuf:"bytes,7,opt,name=md5,proto3" json:"md5,omitempty"`
	Override       bool   `protobuf:"varint,8,opt,name=override,proto3" json:"override,omitempty"`
	Region         string `protobuf:"bytes,9,opt,name=region,proto3" json:"region,omitempty"`
	Registry       string `protobuf:"bytes,10,opt,name=registry,proto3" json:"registry,omitempty"`
	ServiceCode    string `protobuf:"bytes,11,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Tag            string `protobuf:"bytes,12,opt,name=tag,proto3" json:"tag,omitempty"`
	Type           string `protobuf:"bytes,13,opt,name=type,proto3" json:"type,omitempty"`
	Version        string `protobuf:"bytes,14,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *DockerImageTaskReq) Reset() {
	*x = DockerImageTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DockerImageTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DockerImageTaskReq) ProtoMessage() {}

func (x *DockerImageTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DockerImageTaskReq.ProtoReflect.Descriptor instead.
func (*DockerImageTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{12}
}

func (x *DockerImageTaskReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DockerImageTaskReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *DockerImageTaskReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DockerImageTaskReq) GetFileIdentifier() string {
	if x != nil {
		return x.FileIdentifier
	}
	return ""
}

func (x *DockerImageTaskReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *DockerImageTaskReq) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *DockerImageTaskReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *DockerImageTaskReq) GetOverride() bool {
	if x != nil {
		return x.Override
	}
	return false
}

func (x *DockerImageTaskReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *DockerImageTaskReq) GetRegistry() string {
	if x != nil {
		return x.Registry
	}
	return ""
}

func (x *DockerImageTaskReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *DockerImageTaskReq) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *DockerImageTaskReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DockerImageTaskReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DescribeCommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *DescribeCommonReply) Reset() {
	*x = DescribeCommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeCommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeCommonReply) ProtoMessage() {}

func (x *DescribeCommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeCommonReply.ProtoReflect.Descriptor instead.
func (*DescribeCommonReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{13}
}

func (x *DescribeCommonReply) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DescribeCommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DescribeTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int64     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Data    *TaskInfo `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Message string    `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *DescribeTaskReply) Reset() {
	*x = DescribeTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeTaskReply) ProtoMessage() {}

func (x *DescribeTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeTaskReply.ProtoReflect.Descriptor instead.
func (*DescribeTaskReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{14}
}

func (x *DescribeTaskReply) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DescribeTaskReply) GetData() *TaskInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DescribeTaskReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskID string `protobuf:"bytes,1,opt,name=taskID,proto3" json:"taskID,omitempty"`
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{15}
}

func (x *TaskInfo) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

type DescribeTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskID string `protobuf:"bytes,1,opt,name=taskID,proto3" json:"taskID,omitempty"`
}

func (x *DescribeTaskReq) Reset() {
	*x = DescribeTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeTaskReq) ProtoMessage() {}

func (x *DescribeTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeTaskReq.ProtoReflect.Descriptor instead.
func (*DescribeTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{16}
}

func (x *DescribeTaskReq) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

type PushTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateTime string `protobuf:"bytes,1,opt,name=createTime,proto3" json:"createTime,omitempty"`
	Message    string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Retry      int64  `protobuf:"varint,3,opt,name=retry,proto3" json:"retry,omitempty"`
	State      string `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	UpdateTime string `protobuf:"bytes,5,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *PushTaskInfo) Reset() {
	*x = PushTaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTaskInfo) ProtoMessage() {}

func (x *PushTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTaskInfo.ProtoReflect.Descriptor instead.
func (*PushTaskInfo) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{17}
}

func (x *PushTaskInfo) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *PushTaskInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PushTaskInfo) GetRetry() int64 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *PushTaskInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *PushTaskInfo) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type DescribeCommonFileTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int64         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Data    *PushTaskInfo `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Message string        `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *DescribeCommonFileTaskReply) Reset() {
	*x = DescribeCommonFileTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeCommonFileTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeCommonFileTaskReply) ProtoMessage() {}

func (x *DescribeCommonFileTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeCommonFileTaskReply.ProtoReflect.Descriptor instead.
func (*DescribeCommonFileTaskReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{18}
}

func (x *DescribeCommonFileTaskReply) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DescribeCommonFileTaskReply) GetData() *PushTaskInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DescribeCommonFileTaskReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DescribesArtifactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceCode  string `protobuf:"bytes,1,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Version      string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Architecture string `protobuf:"bytes,3,opt,name=architecture,proto3" json:"architecture,omitempty"`
	HostOSType   string `protobuf:"bytes,4,opt,name=hostOSType,proto3" json:"hostOSType,omitempty"`
	AppName      string `protobuf:"bytes,5,opt,name=appName,proto3" json:"appName,omitempty"`
}

func (x *DescribesArtifactReq) Reset() {
	*x = DescribesArtifactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribesArtifactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribesArtifactReq) ProtoMessage() {}

func (x *DescribesArtifactReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribesArtifactReq.ProtoReflect.Descriptor instead.
func (*DescribesArtifactReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{19}
}

func (x *DescribesArtifactReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *DescribesArtifactReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DescribesArtifactReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *DescribesArtifactReq) GetHostOSType() string {
	if x != nil {
		return x.HostOSType
	}
	return ""
}

func (x *DescribesArtifactReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type DescribesArtifactReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegularFile    []*AppArtifact `protobuf:"bytes,1,rep,name=regularFile,proto3" json:"regularFile,omitempty"`
	Rpm            []*AppArtifact `protobuf:"bytes,2,rep,name=rpm,proto3" json:"rpm,omitempty"`
	SkywingImage   []*AppArtifact `protobuf:"bytes,3,rep,name=skywingImage,proto3" json:"skywingImage,omitempty"`
	SkywingPackage []*AppArtifact `protobuf:"bytes,4,rep,name=skywingPackage,proto3" json:"skywingPackage,omitempty"`
	XingyunImage   []*AppArtifact `protobuf:"bytes,5,rep,name=xingyunImage,proto3" json:"xingyunImage,omitempty"`
	XingyunPackage []*AppArtifact `protobuf:"bytes,6,rep,name=xingyunPackage,proto3" json:"xingyunPackage,omitempty"`
}

func (x *DescribesArtifactReply) Reset() {
	*x = DescribesArtifactReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribesArtifactReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribesArtifactReply) ProtoMessage() {}

func (x *DescribesArtifactReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribesArtifactReply.ProtoReflect.Descriptor instead.
func (*DescribesArtifactReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{20}
}

func (x *DescribesArtifactReply) GetRegularFile() []*AppArtifact {
	if x != nil {
		return x.RegularFile
	}
	return nil
}

func (x *DescribesArtifactReply) GetRpm() []*AppArtifact {
	if x != nil {
		return x.Rpm
	}
	return nil
}

func (x *DescribesArtifactReply) GetSkywingImage() []*AppArtifact {
	if x != nil {
		return x.SkywingImage
	}
	return nil
}

func (x *DescribesArtifactReply) GetSkywingPackage() []*AppArtifact {
	if x != nil {
		return x.SkywingPackage
	}
	return nil
}

func (x *DescribesArtifactReply) GetXingyunImage() []*AppArtifact {
	if x != nil {
		return x.XingyunImage
	}
	return nil
}

func (x *DescribesArtifactReply) GetXingyunPackage() []*AppArtifact {
	if x != nil {
		return x.XingyunPackage
	}
	return nil
}

type AppArtifact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName        string `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	AppServiceCode string `protobuf:"bytes,2,opt,name=appServiceCode,proto3" json:"appServiceCode,omitempty"`
	AppVersion     string `protobuf:"bytes,3,opt,name=appVersion,proto3" json:"appVersion,omitempty"`
	FileIdentifier string `protobuf:"bytes,4,opt,name=fileIdentifier,proto3" json:"fileIdentifier,omitempty"`
	HostOsType     string `protobuf:"bytes,5,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
}

func (x *AppArtifact) Reset() {
	*x = AppArtifact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppArtifact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppArtifact) ProtoMessage() {}

func (x *AppArtifact) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppArtifact.ProtoReflect.Descriptor instead.
func (*AppArtifact) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{21}
}

func (x *AppArtifact) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AppArtifact) GetAppServiceCode() string {
	if x != nil {
		return x.AppServiceCode
	}
	return ""
}

func (x *AppArtifact) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *AppArtifact) GetFileIdentifier() string {
	if x != nil {
		return x.FileIdentifier
	}
	return ""
}

func (x *AppArtifact) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

type CreateArtifactPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Architecture                  string   `protobuf:"bytes,1,opt,name=architecture,proto3" json:"architecture,omitempty"`
	BaseVersion                   string   `protobuf:"bytes,2,opt,name=baseVersion,proto3" json:"baseVersion,omitempty"`
	DestHostIp                    string   `protobuf:"bytes,3,opt,name=destHostIp,proto3" json:"destHostIp,omitempty"`
	DestPath                      string   `protobuf:"bytes,4,opt,name=destPath,proto3" json:"destPath,omitempty"`
	DestSshPort                   int64    `protobuf:"varint,5,opt,name=destSshPort,proto3" json:"destSshPort,omitempty"`
	HostOsTypes                   []string `protobuf:"bytes,6,rep,name=hostOsTypes,proto3" json:"hostOsTypes,omitempty"`
	PackageVersion                string   `protobuf:"bytes,7,opt,name=packageVersion,proto3" json:"packageVersion,omitempty"`
	PackageVersionType            string   `protobuf:"bytes,8,opt,name=packageVersionType,proto3" json:"packageVersionType,omitempty"`
	SnapshotGeneratedTimeStamp    int64    `protobuf:"varint,9,opt,name=snapshotGeneratedTimeStamp,proto3" json:"snapshotGeneratedTimeStamp,omitempty"`
	TaskType                      string   `protobuf:"bytes,10,opt,name=taskType,proto3" json:"taskType,omitempty"`
	JdstackReleaseS3              string   `protobuf:"bytes,11,opt,name=jdstackReleaseS3,proto3" json:"jdstackReleaseS3,omitempty"`
	ClearArtifactDirBeforePackage bool     `protobuf:"varint,12,opt,name=clearArtifactDirBeforePackage,proto3" json:"clearArtifactDirBeforePackage,omitempty"`
	SpecResources                 []string `protobuf:"bytes,13,rep,name=specResources,proto3" json:"specResources,omitempty"`
}

func (x *CreateArtifactPackageReq) Reset() {
	*x = CreateArtifactPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateArtifactPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateArtifactPackageReq) ProtoMessage() {}

func (x *CreateArtifactPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateArtifactPackageReq.ProtoReflect.Descriptor instead.
func (*CreateArtifactPackageReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{22}
}

func (x *CreateArtifactPackageReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetBaseVersion() string {
	if x != nil {
		return x.BaseVersion
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetDestHostIp() string {
	if x != nil {
		return x.DestHostIp
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetDestPath() string {
	if x != nil {
		return x.DestPath
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetDestSshPort() int64 {
	if x != nil {
		return x.DestSshPort
	}
	return 0
}

func (x *CreateArtifactPackageReq) GetHostOsTypes() []string {
	if x != nil {
		return x.HostOsTypes
	}
	return nil
}

func (x *CreateArtifactPackageReq) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetPackageVersionType() string {
	if x != nil {
		return x.PackageVersionType
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetSnapshotGeneratedTimeStamp() int64 {
	if x != nil {
		return x.SnapshotGeneratedTimeStamp
	}
	return 0
}

func (x *CreateArtifactPackageReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetJdstackReleaseS3() string {
	if x != nil {
		return x.JdstackReleaseS3
	}
	return ""
}

func (x *CreateArtifactPackageReq) GetClearArtifactDirBeforePackage() bool {
	if x != nil {
		return x.ClearArtifactDirBeforePackage
	}
	return false
}

func (x *CreateArtifactPackageReq) GetSpecResources() []string {
	if x != nil {
		return x.SpecResources
	}
	return nil
}

type DescribeArtifactPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageVersion string `protobuf:"bytes,1,opt,name=packageVersion,proto3" json:"packageVersion,omitempty"`
	Architecture   string `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	HostOsType     string `protobuf:"bytes,3,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
}

func (x *DescribeArtifactPackageReq) Reset() {
	*x = DescribeArtifactPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeArtifactPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeArtifactPackageReq) ProtoMessage() {}

func (x *DescribeArtifactPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeArtifactPackageReq.ProtoReflect.Descriptor instead.
func (*DescribeArtifactPackageReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{23}
}

func (x *DescribeArtifactPackageReq) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *DescribeArtifactPackageReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *DescribeArtifactPackageReq) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

type DescribeArtifactPackageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ErrorMsg string `protobuf:"bytes,2,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
	Log      string `protobuf:"bytes,3,opt,name=log,proto3" json:"log,omitempty"`
}

func (x *DescribeArtifactPackageReply) Reset() {
	*x = DescribeArtifactPackageReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeArtifactPackageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeArtifactPackageReply) ProtoMessage() {}

func (x *DescribeArtifactPackageReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeArtifactPackageReply.ProtoReflect.Descriptor instead.
func (*DescribeArtifactPackageReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{24}
}

func (x *DescribeArtifactPackageReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DescribeArtifactPackageReply) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *DescribeArtifactPackageReply) GetLog() string {
	if x != nil {
		return x.Log
	}
	return ""
}

type CreateMetadataPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseVersion    string `protobuf:"bytes,1,opt,name=baseVersion,proto3" json:"baseVersion,omitempty"`
	DestHostIp     string `protobuf:"bytes,2,opt,name=destHostIp,proto3" json:"destHostIp,omitempty"`
	DestPath       string `protobuf:"bytes,3,opt,name=destPath,proto3" json:"destPath,omitempty"`
	DestSshPort    int64  `protobuf:"varint,4,opt,name=destSshPort,proto3" json:"destSshPort,omitempty"`
	PackageVersion string `protobuf:"bytes,5,opt,name=packageVersion,proto3" json:"packageVersion,omitempty"`
	TaskType       string `protobuf:"bytes,6,opt,name=taskType,proto3" json:"taskType,omitempty"`
}

func (x *CreateMetadataPackageReq) Reset() {
	*x = CreateMetadataPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMetadataPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMetadataPackageReq) ProtoMessage() {}

func (x *CreateMetadataPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMetadataPackageReq.ProtoReflect.Descriptor instead.
func (*CreateMetadataPackageReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{25}
}

func (x *CreateMetadataPackageReq) GetBaseVersion() string {
	if x != nil {
		return x.BaseVersion
	}
	return ""
}

func (x *CreateMetadataPackageReq) GetDestHostIp() string {
	if x != nil {
		return x.DestHostIp
	}
	return ""
}

func (x *CreateMetadataPackageReq) GetDestPath() string {
	if x != nil {
		return x.DestPath
	}
	return ""
}

func (x *CreateMetadataPackageReq) GetDestSshPort() int64 {
	if x != nil {
		return x.DestSshPort
	}
	return 0
}

func (x *CreateMetadataPackageReq) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *CreateMetadataPackageReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

type PackageTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DestHostIp     string `protobuf:"bytes,1,opt,name=destHostIp,proto3" json:"destHostIp,omitempty"`
	DestPath       string `protobuf:"bytes,2,opt,name=destPath,proto3" json:"destPath,omitempty"`
	PackageVersion string `protobuf:"bytes,3,opt,name=packageVersion,proto3" json:"packageVersion,omitempty"`
	DestSshPort    int64  `protobuf:"varint,4,opt,name=destSshPort,proto3" json:"destSshPort,omitempty"`
	State          string `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Message        string `protobuf:"bytes,6,opt,name=message,proto3" json:"message,omitempty"`
	Architecture   string `protobuf:"bytes,7,opt,name=architecture,proto3" json:"architecture,omitempty"`
	HostOsType     string `protobuf:"bytes,8,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	CreateTime     string `protobuf:"bytes,9,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime     string `protobuf:"bytes,10,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *PackageTaskInfo) Reset() {
	*x = PackageTaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PackageTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageTaskInfo) ProtoMessage() {}

func (x *PackageTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageTaskInfo.ProtoReflect.Descriptor instead.
func (*PackageTaskInfo) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{26}
}

func (x *PackageTaskInfo) GetDestHostIp() string {
	if x != nil {
		return x.DestHostIp
	}
	return ""
}

func (x *PackageTaskInfo) GetDestPath() string {
	if x != nil {
		return x.DestPath
	}
	return ""
}

func (x *PackageTaskInfo) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *PackageTaskInfo) GetDestSshPort() int64 {
	if x != nil {
		return x.DestSshPort
	}
	return 0
}

func (x *PackageTaskInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *PackageTaskInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PackageTaskInfo) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *PackageTaskInfo) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *PackageTaskInfo) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *PackageTaskInfo) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type DescribeMetadataPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageVersion string `protobuf:"bytes,1,opt,name=packageVersion,proto3" json:"packageVersion,omitempty"`
	CreateTime     string `protobuf:"bytes,2,opt,name=createTime,proto3" json:"createTime,omitempty"`
}

func (x *DescribeMetadataPackageReq) Reset() {
	*x = DescribeMetadataPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeMetadataPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeMetadataPackageReq) ProtoMessage() {}

func (x *DescribeMetadataPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeMetadataPackageReq.ProtoReflect.Descriptor instead.
func (*DescribeMetadataPackageReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{27}
}

func (x *DescribeMetadataPackageReq) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *DescribeMetadataPackageReq) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

type DescribeSCFileTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int64                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Data    *DescribeSCFilesResponseData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Message string                       `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *DescribeSCFileTaskReply) Reset() {
	*x = DescribeSCFileTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSCFileTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSCFileTaskReply) ProtoMessage() {}

func (x *DescribeSCFileTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSCFileTaskReply.ProtoReflect.Descriptor instead.
func (*DescribeSCFileTaskReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{28}
}

func (x *DescribeSCFileTaskReply) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DescribeSCFileTaskReply) GetData() *DescribeSCFilesResponseData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DescribeSCFileTaskReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DescribeSCFilesResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName      string `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	Architecture string `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	CreateTime   string `protobuf:"bytes,3,opt,name=createTime,proto3" json:"createTime,omitempty"`
	HostOsType   string `protobuf:"bytes,4,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	Message      string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Region       string `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	ServiceCode  string `protobuf:"bytes,7,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	State        string `protobuf:"bytes,8,opt,name=state,proto3" json:"state,omitempty"`
	UpdateTime   string `protobuf:"bytes,9,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	Version      string `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *DescribeSCFilesResponseData) Reset() {
	*x = DescribeSCFilesResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSCFilesResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSCFilesResponseData) ProtoMessage() {}

func (x *DescribeSCFilesResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSCFilesResponseData.ProtoReflect.Descriptor instead.
func (*DescribeSCFilesResponseData) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{29}
}

func (x *DescribeSCFilesResponseData) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *DescribeSCFilesResponseData) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type CreateSCFilesTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceCode  string `protobuf:"bytes,1,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	Version      string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Region       string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Architecture string `protobuf:"bytes,4,opt,name=architecture,proto3" json:"architecture,omitempty"`
	HostOsType   string `protobuf:"bytes,5,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	AppName      string `protobuf:"bytes,6,opt,name=appName,proto3" json:"appName,omitempty"`
}

func (x *CreateSCFilesTaskReq) Reset() {
	*x = CreateSCFilesTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSCFilesTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSCFilesTaskReq) ProtoMessage() {}

func (x *CreateSCFilesTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSCFilesTaskReq.ProtoReflect.Descriptor instead.
func (*CreateSCFilesTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{30}
}

func (x *CreateSCFilesTaskReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *CreateSCFilesTaskReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateSCFilesTaskReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateSCFilesTaskReq) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *CreateSCFilesTaskReq) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *CreateSCFilesTaskReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type AppFilesTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version       string          `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	ServiceCode   string          `protobuf:"bytes,2,opt,name=serviceCode,proto3" json:"serviceCode,omitempty"`
	ProCode       string          `protobuf:"bytes,3,opt,name=proCode,proto3" json:"proCode,omitempty"`
	AppCode       string          `protobuf:"bytes,4,opt,name=appCode,proto3" json:"appCode,omitempty"`
	AppVersionID  string          `protobuf:"bytes,5,opt,name=appVersionID,proto3" json:"appVersionID,omitempty"`
	DeployMode    string          `protobuf:"bytes,6,opt,name=deployMode,proto3" json:"deployMode,omitempty"`
	EnvName       string          `protobuf:"bytes,7,opt,name=envName,proto3" json:"envName,omitempty"`
	Region        string          `protobuf:"bytes,8,opt,name=region,proto3" json:"region,omitempty"`
	ArtifactInfos []*PushFileInfo `protobuf:"bytes,9,rep,name=artifactInfos,proto3" json:"artifactInfos,omitempty"`
}

func (x *AppFilesTaskReq) Reset() {
	*x = AppFilesTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppFilesTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppFilesTaskReq) ProtoMessage() {}

func (x *AppFilesTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppFilesTaskReq.ProtoReflect.Descriptor instead.
func (*AppFilesTaskReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{31}
}

func (x *AppFilesTaskReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AppFilesTaskReq) GetServiceCode() string {
	if x != nil {
		return x.ServiceCode
	}
	return ""
}

func (x *AppFilesTaskReq) GetProCode() string {
	if x != nil {
		return x.ProCode
	}
	return ""
}

func (x *AppFilesTaskReq) GetAppCode() string {
	if x != nil {
		return x.AppCode
	}
	return ""
}

func (x *AppFilesTaskReq) GetAppVersionID() string {
	if x != nil {
		return x.AppVersionID
	}
	return ""
}

func (x *AppFilesTaskReq) GetDeployMode() string {
	if x != nil {
		return x.DeployMode
	}
	return ""
}

func (x *AppFilesTaskReq) GetEnvName() string {
	if x != nil {
		return x.EnvName
	}
	return ""
}

func (x *AppFilesTaskReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AppFilesTaskReq) GetArtifactInfos() []*PushFileInfo {
	if x != nil {
		return x.ArtifactInfos
	}
	return nil
}

type PushFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileType        string `protobuf:"bytes,1,opt,name=fileType,proto3" json:"fileType,omitempty"`
	FileName        string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName,omitempty"`
	Md5             string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	Arch            string `protobuf:"bytes,4,opt,name=arch,proto3" json:"arch,omitempty"`
	Os              string `protobuf:"bytes,5,opt,name=os,proto3" json:"os,omitempty"`
	FileStorageMode string `protobuf:"bytes,6,opt,name=fileStorageMode,proto3" json:"fileStorageMode,omitempty"`
	FileBucket      string `protobuf:"bytes,7,opt,name=fileBucket,proto3" json:"fileBucket,omitempty"`
	RpmRepo         string `protobuf:"bytes,8,opt,name=rpmRepo,proto3" json:"rpmRepo,omitempty"`
	ImageRepo       string `protobuf:"bytes,9,opt,name=imageRepo,proto3" json:"imageRepo,omitempty"`
	ImageTag        string `protobuf:"bytes,10,opt,name=imageTag,proto3" json:"imageTag,omitempty"`
	PkgModelName    string `protobuf:"bytes,11,opt,name=pkgModelName,proto3" json:"pkgModelName,omitempty"`
	PkgVersion      string `protobuf:"bytes,12,opt,name=pkgVersion,proto3" json:"pkgVersion,omitempty"`
	Identity        string `protobuf:"bytes,13,opt,name=identity,proto3" json:"identity,omitempty"`
	Version         string `protobuf:"bytes,14,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *PushFileInfo) Reset() {
	*x = PushFileInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushFileInfo) ProtoMessage() {}

func (x *PushFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushFileInfo.ProtoReflect.Descriptor instead.
func (*PushFileInfo) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{32}
}

func (x *PushFileInfo) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *PushFileInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *PushFileInfo) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *PushFileInfo) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *PushFileInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *PushFileInfo) GetFileStorageMode() string {
	if x != nil {
		return x.FileStorageMode
	}
	return ""
}

func (x *PushFileInfo) GetFileBucket() string {
	if x != nil {
		return x.FileBucket
	}
	return ""
}

func (x *PushFileInfo) GetRpmRepo() string {
	if x != nil {
		return x.RpmRepo
	}
	return ""
}

func (x *PushFileInfo) GetImageRepo() string {
	if x != nil {
		return x.ImageRepo
	}
	return ""
}

func (x *PushFileInfo) GetImageTag() string {
	if x != nil {
		return x.ImageTag
	}
	return ""
}

func (x *PushFileInfo) GetPkgModelName() string {
	if x != nil {
		return x.PkgModelName
	}
	return ""
}

func (x *PushFileInfo) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *PushFileInfo) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *PushFileInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DescribeAppFileTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ErrorMsg string `protobuf:"bytes,2,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
	Log      string `protobuf:"bytes,3,opt,name=log,proto3" json:"log,omitempty"`
}

func (x *DescribeAppFileTaskReply) Reset() {
	*x = DescribeAppFileTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppFileTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppFileTaskReply) ProtoMessage() {}

func (x *DescribeAppFileTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppFileTaskReply.ProtoReflect.Descriptor instead.
func (*DescribeAppFileTaskReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{33}
}

func (x *DescribeAppFileTaskReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DescribeAppFileTaskReply) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *DescribeAppFileTaskReply) GetLog() string {
	if x != nil {
		return x.Log
	}
	return ""
}

type CreateArtifactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version        string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	AppCode        string `protobuf:"bytes,2,opt,name=appCode,proto3" json:"appCode,omitempty"`
	Arch           string `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
	HostOsType     string `protobuf:"bytes,4,opt,name=hostOsType,proto3" json:"hostOsType,omitempty"`
	FileIdentifier string `protobuf:"bytes,5,opt,name=fileIdentifier,proto3" json:"fileIdentifier,omitempty"`
	DirectorySpec  string `protobuf:"bytes,6,opt,name=directorySpec,proto3" json:"directorySpec,omitempty"`
	Filename       string `protobuf:"bytes,7,opt,name=filename,proto3" json:"filename,omitempty"`
	FileHash       string `protobuf:"bytes,8,opt,name=fileHash,proto3" json:"fileHash,omitempty"`
	FileSize       int64  `protobuf:"varint,9,opt,name=fileSize,proto3" json:"fileSize,omitempty"`
	ImageTags      string `protobuf:"bytes,10,opt,name=imageTags,proto3" json:"imageTags,omitempty"`
	FileVersion    string `protobuf:"bytes,11,opt,name=fileVersion,proto3" json:"fileVersion,omitempty"`
	Param          string `protobuf:"bytes,12,opt,name=param,proto3" json:"param,omitempty"`
}

func (x *CreateArtifactReq) Reset() {
	*x = CreateArtifactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateArtifactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateArtifactReq) ProtoMessage() {}

func (x *CreateArtifactReq) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateArtifactReq.ProtoReflect.Descriptor instead.
func (*CreateArtifactReq) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{34}
}

func (x *CreateArtifactReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateArtifactReq) GetAppCode() string {
	if x != nil {
		return x.AppCode
	}
	return ""
}

func (x *CreateArtifactReq) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *CreateArtifactReq) GetHostOsType() string {
	if x != nil {
		return x.HostOsType
	}
	return ""
}

func (x *CreateArtifactReq) GetFileIdentifier() string {
	if x != nil {
		return x.FileIdentifier
	}
	return ""
}

func (x *CreateArtifactReq) GetDirectorySpec() string {
	if x != nil {
		return x.DirectorySpec
	}
	return ""
}

func (x *CreateArtifactReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *CreateArtifactReq) GetFileHash() string {
	if x != nil {
		return x.FileHash
	}
	return ""
}

func (x *CreateArtifactReq) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *CreateArtifactReq) GetImageTags() string {
	if x != nil {
		return x.ImageTags
	}
	return ""
}

func (x *CreateArtifactReq) GetFileVersion() string {
	if x != nil {
		return x.FileVersion
	}
	return ""
}

func (x *CreateArtifactReq) GetParam() string {
	if x != nil {
		return x.Param
	}
	return ""
}

// 新增响应消息
type DeploymentServerInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	// 为未来扩展预留字段
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *DeploymentServerInfoReply) Reset() {
	*x = DeploymentServerInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jdstack_filecenter_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentServerInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentServerInfoReply) ProtoMessage() {}

func (x *DeploymentServerInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_jdstack_filecenter_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentServerInfoReply.ProtoReflect.Descriptor instead.
func (*DeploymentServerInfoReply) Descriptor() ([]byte, []int) {
	return file_jdstack_filecenter_proto_rawDescGZIP(), []int{35}
}

func (x *DeploymentServerInfoReply) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DeploymentServerInfoReply) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

var File_jdstack_filecenter_proto protoreflect.FileDescriptor

var file_jdstack_filecenter_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76,
	0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x0b, 0x0a,
	0x11, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x12, 0x96, 0x01, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x76, 0x92, 0x41, 0x6c, 0x32, 0x6a, 0xe6, 0x96,
	0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0x0a, 0x2d, 0x20, 0xe7, 0xad, 0x89,
	0xe4, 0xba, 0x8e, 0xe4, 0xb8, 0x8a, 0xe7, 0xba, 0xa7, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0xe6, 0x88, 0x96, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xef, 0xbc, 0x8c, 0xe7,
	0x9b, 0xb8, 0xe5, 0x90, 0x8c, 0xe4, 0xb8, 0x8a, 0xe7, 0xba, 0xa7, 0xe3, 0x80, 0x81, 0xe6, 0x9e,
	0xb6, 0xe6, 0x9e, 0x84, 0xe3, 0x80, 0x81, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0x9a, 0x84,
	0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0xe4, 0xb8, 0x8d, 0xe5,
	0xbe, 0x97, 0xe9, 0x87, 0x8d, 0xe5, 0xa4, 0x8d, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0xc7, 0x02, 0x0a, 0x0a,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0xa6, 0x02, 0x92, 0x41, 0x92, 0x01, 0x32, 0x8f, 0x01, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2,
	0xe6, 0xa8, 0xa1, 0xe5, 0xbc, 0x8f, 0x3a, 0x0a, 0x5b, 0x20, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x20, 0x73, 0x6b, 0x79, 0x77,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x6d, 0x61, 0x74, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x20, 0x73, 0x68, 0x65, 0x6c, 0x6c,
	0x20, 0x68, 0x65, 0x6c, 0x6d, 0x20, 0x72, 0x70, 0x6d, 0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x62,
	0x69, 0x6e, 0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x32, 0x20, 0x69, 0x61, 0x61, 0x73, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x20, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x20, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x5d, 0xfa, 0x42, 0x8c, 0x01, 0x72, 0x89, 0x01,
	0x52, 0x11, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x52, 0x0f, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x0e, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x52, 0x05, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x52, 0x04, 0x68, 0x65, 0x6c,
	0x6d, 0x52, 0x03, 0x72, 0x70, 0x6d, 0x52, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x69, 0x6e,
	0x52, 0x07, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x32, 0x52, 0x0a, 0x69, 0x61, 0x61, 0x73, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x11, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0f, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75,
	0x6e, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x32, 0x25, 0xe6, 0x96,
	0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae,
	0xa4, 0xe4, 0xbb, 0x8e, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xe5, 0x86, 0x85, 0xe6, 0x8f, 0x90,
	0xe5, 0x8f, 0x96, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a,
	0x92, 0x41, 0x20, 0x32, 0x1e, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe3, 0x80, 0x81, 0xe5, 0x8c,
	0x85, 0xe3, 0x80, 0x81, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0xba, 0x90, 0xe5, 0x9c, 0xb0,
	0xe5, 0x9d, 0x80, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x5e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3e, 0x92, 0x41, 0x3b, 0x32, 0x39, 0xe6, 0xa8,
	0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0x0a, 0x2d, 0x20, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x5b, 0x20, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x20, 0x5d,
	0x20, 0xe5, 0xbf, 0x85, 0xe5, 0xa1, 0xab, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0x92, 0x41, 0x30, 0x32, 0x2e, 0xe6, 0x96, 0x87,
	0xe4, 0xbb, 0xb6, 0xe6, 0x88, 0x96, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe4, 0xbb, 0x8e, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0xe5, 0x86, 0x85, 0xe6, 0x8f, 0x90, 0xe5, 0x8f, 0x96, 0x52, 0x09, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x54, 0x61, 0x67, 0x73, 0x12, 0x69, 0x0a, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x47, 0x92, 0x41, 0x44,
	0x32, 0x42, 0xe5, 0x8c, 0x85, 0xe5, 0xad, 0x98, 0xe5, 0x82, 0xa8, 0xe4, 0xbd, 0x8d, 0xe7, 0xbd,
	0xae, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x0a, 0x2d, 0x20, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x5b, 0x20, 0x73, 0x33, 0x20, 0x5d, 0x20, 0xe5, 0xbf,
	0x85, 0xe5, 0xa1, 0xab, 0x3a, 0x20, 0x5b, 0x20, 0x68, 0x74, 0x74, 0x70, 0x20, 0x6d, 0x69, 0x6e,
	0x69, 0x6f, 0x20, 0x5d, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x57, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0x92, 0x41, 0x2e, 0x32, 0x2c, 0xe5,
	0x8c, 0x85, 0xe5, 0xad, 0x98, 0xe5, 0x82, 0xa8, 0xe4, 0xbd, 0x8d, 0xe7, 0xbd, 0xae, 0x0a, 0x2d,
	0x20, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x5b, 0x20,
	0x73, 0x33, 0x20, 0x5d, 0x20, 0xe5, 0xbf, 0x85, 0xe5, 0xa1, 0xab, 0x52, 0x0d, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x7a, 0x0a, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x4b, 0x69, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x5e, 0x92, 0x41,
	0x5b, 0x32, 0x59, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x0a,
	0x2d, 0x20, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x5b,
	0x20, 0x72, 0x70, 0x6d, 0x20, 0x5d, 0x20, 0xe5, 0xbf, 0x85, 0xe5, 0xa1, 0xab, 0xe5, 0x8f, 0xaf,
	0xe9, 0x80, 0x89, 0x3a, 0x20, 0x5b, 0x20, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x20, 0x69,
	0x61, 0x61, 0x73, 0x2d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x20, 0x6f, 0x65, 0x2d, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x20, 0x2e, 0x2e, 0x2e, 0x20, 0x5d, 0x52, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x3e, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x22, 0x92, 0x41, 0x1f, 0x32, 0x1d, 0xe5,
	0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0xe4, 0xbb, 0x93, 0xe5, 0xba,
	0x93, 0xe7, 0x9a, 0x84, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x49, 0x44, 0x52, 0x08, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x4e, 0x92, 0x41, 0x29, 0x32, 0x27, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7,
	0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0x3a, 0x0a, 0x5b, 0x20, 0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73, 0x20,
	0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x20, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x5d,
	0xfa, 0x42, 0x1f, 0x72, 0x1d, 0x52, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73, 0x52, 0x09, 0x6f,
	0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x52, 0x05, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0xd0,
	0x01, 0x01, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x41, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0x92, 0x41, 0x18, 0x32, 0x16, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e,
	0x84, 0x3a, 0x0a, 0x5b, 0x20, 0x78, 0x38, 0x36, 0x5f, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x20,
	0x5d, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x52, 0x06, 0x78, 0x38, 0x36, 0x5f, 0x36, 0x34, 0x52, 0x03,
	0x61, 0x72, 0x6d, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x3a, 0x27, 0x92, 0x41, 0x24, 0x0a, 0x22,
	0xd2, 0x01, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0xd2, 0x01, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x74, 0x79, 0x70, 0x65, 0xd2, 0x01, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0xd4, 0x06, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x44, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x0f,
	0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa,
	0x42, 0x15, 0x72, 0x13, 0x20, 0x01, 0x32, 0x0f, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a,
	0x41, 0x2d, 0x5a, 0x2e, 0x5d, 0x2b, 0x24, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x76, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x5e, 0x92, 0x41, 0x5b, 0x32, 0x59, 0xe7, 0xad, 0x96, 0xe7, 0x95, 0xa5, 0x3a, 0x0a, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x20, 0x2d, 0x20, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96,
	0xe7, 0x9b, 0xb8, 0xe5, 0x90, 0x8c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0xe3, 0x80, 0x81, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0xe3, 0x80, 0x81, 0x61, 0x72,
	0x63, 0x68, 0xe3, 0x80, 0x81, 0x6f, 0x73, 0xe3, 0x80, 0x81, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x46, 0x0a, 0x07, 0x69, 0x73, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x2c, 0x92, 0x41, 0x29, 0x32, 0x27,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe8, 0xa6, 0x81, 0xe5, 0xb0, 0x86, 0xe6, 0x96, 0x87, 0xe4,
	0xbb, 0xb6, 0xe6, 0x88, 0x96, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe4, 0xbf, 0xa1, 0xe6, 0x81,
	0xaf, 0xe8, 0x90, 0xbd, 0xe5, 0xba, 0x93, 0x52, 0x07, 0x69, 0x73, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xba, 0xa7, 0xe5,
	0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x04, 0x61, 0x72, 0x63,
	0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0x92, 0x41, 0x18, 0x32, 0x16, 0xe6, 0x9e,
	0xb6, 0xe6, 0x9e, 0x84, 0x3a, 0x0a, 0x5b, 0x20, 0x78, 0x38, 0x36, 0x5f, 0x36, 0x34, 0x20, 0x61,
	0x72, 0x6d, 0x20, 0x5d, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x52, 0x06, 0x78, 0x38, 0x36, 0x5f, 0x36,
	0x34, 0x52, 0x03, 0x61, 0x72, 0x6d, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x5b, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4b, 0x92, 0x41, 0x29, 0x32, 0x27, 0xe6,
	0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0x3a, 0x0a, 0x5b, 0x20, 0x63,
	0x65, 0x6e, 0x74, 0x6f, 0x73, 0x20, 0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x20,
	0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x5d, 0xfa, 0x42, 0x1c, 0x72, 0x1a, 0x52, 0x06, 0x63, 0x65, 0x6e,
	0x74, 0x6f, 0x73, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x52, 0x05,
	0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x65, 0x0a, 0x05, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x42, 0x19,
	0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe4, 0xbf, 0xa1, 0xe6, 0x81,
	0xaf, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x93, 0x01, 0x0a, 0x07, 0x65, 0x6e, 0x76, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x79, 0x92, 0x41, 0x5b, 0x32, 0x59, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe7,
	0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x2c, 0xe9, 0x9c, 0x80, 0x46,
	0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0xe8, 0x84, 0x9a, 0xe6, 0x9c, 0xac, 0xe6,
	0x94, 0xaf, 0xe6, 0x8c, 0x81, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe7, 0xa9, 0xba, 0x3d,
	0xe4, 0xbb, 0x85, 0xe6, 0x94, 0xb9, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0xe4, 0xb8, 0x8d, 0xe5, 0xbe, 0x80, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe6, 0x8e, 0xa8, 0xe9,
	0x80, 0x81, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d,
	0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x2d, 0x5f, 0x5d, 0x2b, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x65,
	0x6e, 0x76, 0x4e, 0x61, 0x6d, 0x65, 0x3a, 0x31, 0x92, 0x41, 0x2e, 0x0a, 0x2c, 0xd2, 0x01, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x04, 0x61, 0x72, 0x63, 0x68, 0xd2, 0x01, 0x02, 0x6f,
	0x73, 0xd2, 0x01, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xbc, 0x03, 0x0a, 0x15, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4,
	0xbb, 0xb6, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x32, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x43, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0x41, 0x19, 0x32, 0x17, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e,
	0x84, 0x3a, 0x0a, 0x5b, 0x20, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x36, 0x34,
	0x20, 0x5d, 0xfa, 0x42, 0x10, 0x72, 0x0e, 0x52, 0x05, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x52, 0x05,
	0x61, 0x72, 0x6d, 0x36, 0x34, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x8f, 0x01, 0x0a, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x73,
	0x92, 0x41, 0x3c, 0x32, 0x3a, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x3a, 0x0a, 0x5b, 0x20, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x20, 0x6f, 0x75, 0x74, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x20, 0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x20, 0x72, 0x70, 0x6d,
	0x20, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x68, 0x65, 0x6c, 0x6d, 0x20, 0x5d, 0xfa,
	0x42, 0x31, 0x72, 0x2f, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x09, 0x6f, 0x75, 0x74,
	0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x03, 0x72, 0x70, 0x6d, 0x52, 0x07, 0x6f, 0x73, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x04, 0x68,
	0x65, 0x6c, 0x6d, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a,
	0x0c, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x32, 0x0e, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8,
	0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x69, 0x64, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x3a, 0x30, 0x92, 0x41, 0x2d, 0x0a, 0x2b, 0xd2, 0x01, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0xd2, 0x01, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x04, 0x61, 0x72, 0x63, 0x68, 0xd2, 0x01, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2d, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0x92,
	0x41, 0x05, 0x32, 0x03, 0x6d, 0x64, 0x35, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x2d, 0x0a, 0x08,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0xe4, 0xbf, 0xa1, 0xe6, 0x81,
	0xaf, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x87, 0x05, 0x0a, 0x24,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x41, 0x6e,
	0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xb9,
	0xb3, 0xe5, 0x8f, 0xb0, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53,
	0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x20,
	0x01, 0x32, 0x0f, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5d,
	0x2b, 0x24, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0xa7, 0x01, 0x0a, 0x06,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x8e, 0x01, 0x92,
	0x41, 0x8a, 0x01, 0x32, 0x87, 0x01, 0xe7, 0xad, 0x96, 0xe7, 0x95, 0xa5, 0x3a, 0x0a, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x20, 0x2d, 0x20, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe7,
	0x9b, 0xb8, 0xe5, 0x90, 0x8c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0xe3, 0x80, 0x81, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0xe3, 0x80, 0x81, 0x61, 0x72, 0x63,
	0x68, 0xe3, 0x80, 0x81, 0x6f, 0x73, 0xe3, 0x80, 0x81, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x20,
	0x0a, 0x61, 0x75, 0x74, 0x6f, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x74, 0x68, 0x65,
	0x72, 0x73, 0x20, 0x2d, 0x20, 0xe8, 0x87, 0xaa, 0xe5, 0x8a, 0xa8, 0xe4, 0xb8, 0x8b, 0xe7, 0xba,
	0xbf, 0xe5, 0x85, 0xb6, 0xe4, 0xbb, 0x96, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0x52, 0x06, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x32, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x32,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x32, 0x0c, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x31, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92,
	0x41, 0x0c, 0x32, 0x0a, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x62, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5,
	0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x3a, 0x0f, 0x92, 0x41, 0x0c, 0x0a, 0x0a, 0xd2, 0x01, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf2, 0x06, 0x0a, 0x22, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x65, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x09,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xb9, 0xb3, 0xe5, 0x8f, 0xb0, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a,
	0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x20, 0x01, 0x32, 0x0f, 0x5e, 0x5b, 0x30, 0x2d, 0x39,
	0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x2e, 0x5d, 0x2b, 0x24, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x76, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x5e, 0x92, 0x41, 0x5b, 0x32, 0x59, 0xe7, 0xad, 0x96, 0xe7, 0x95, 0xa5,
	0x3a, 0x0a, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x20, 0x2d, 0x20, 0xe8, 0xa6, 0x86,
	0xe7, 0x9b, 0x96, 0xe7, 0x9b, 0xb8, 0xe5, 0x90, 0x8c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0xe3, 0x80, 0x81, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0xe3, 0x80,
	0x81, 0x61, 0x72, 0x63, 0x68, 0xe3, 0x80, 0x81, 0x6f, 0x73, 0xe3, 0x80, 0x81, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe4, 0xbf, 0xa1,
	0xe6, 0x81, 0xaf, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x32, 0x0a, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x2d, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xe5, 0x90,
	0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x31,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x32, 0x0a, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xba, 0xa7,
	0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x64, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe4, 0xbf, 0xa1, 0xe6,
	0x81, 0xaf, 0x52, 0x08, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x2f, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x59, 0x61, 0x6d, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x32, 0x08, 0x70, 0x64, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x59, 0x61, 0x6d, 0x6c, 0x12, 0x5d, 0x0a,
	0x08, 0x61, 0x64, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x64, 0x6c,
	0x52, 0x65, 0x71, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81,
	0xe7, 0xba, 0xa7, 0xe5, 0x88, 0xab, 0xe7, 0x9a, 0x84, 0x61, 0x64, 0x6c, 0xe6, 0x96, 0x87, 0xe4,
	0xbb, 0xb6, 0x52, 0x08, 0x61, 0x64, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x8a, 0x01, 0x0a,
	0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x42, 0x32, 0x92, 0x41, 0x2f, 0x32, 0x2d, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81,
	0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x9a, 0x84, 0xe7, 0x9a, 0x84, 0xe5, 0x95, 0x86, 0xe4,
	0xb8, 0x9a, 0xe5, 0xb9, 0xb3, 0xe5, 0x8f, 0xb0, 0xe5, 0x85, 0x83, 0xe6, 0x95, 0xb0, 0xe6, 0x8d,
	0xae, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x3a, 0x0f, 0x92, 0x41, 0x0c, 0x0a, 0x0a,
	0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x88, 0x01, 0x0a, 0x26, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x41,
	0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x65, 0x61, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x31, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x93, 0x8d,
	0xe4, 0xbd, 0x9c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c,
	0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xf9, 0x05, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92,
	0x41, 0x0c, 0x32, 0x0a, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x32, 0x0a, 0xe5,
	0xba, 0x94, 0xe7, 0x94, 0xa8, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0xc7, 0x02, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0xa6, 0x02, 0x92, 0x41, 0x92, 0x01, 0x32, 0x8f,
	0x01, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe6, 0xa8, 0xa1, 0xe5, 0xbc, 0x8f, 0x3a, 0x0a, 0x5b,
	0x20, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x20, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x20, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x20, 0x68, 0x65, 0x6c, 0x6d, 0x20, 0x72, 0x70, 0x6d,
	0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x76,
	0x32, 0x20, 0x69, 0x61, 0x61, 0x73, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x20, 0x78, 0x69, 0x6e,
	0x67, 0x79, 0x75, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x20, 0x78,
	0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x5d,
	0xfa, 0x42, 0x8c, 0x01, 0x72, 0x89, 0x01, 0x52, 0x11, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0f, 0x73, 0x6b, 0x79, 0x77,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x0e, 0x6d, 0x61, 0x74,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x05, 0x73, 0x68, 0x65,
	0x6c, 0x6c, 0x52, 0x04, 0x68, 0x65, 0x6c, 0x6d, 0x52, 0x03, 0x72, 0x70, 0x6d, 0x52, 0x08, 0x6d,
	0x61, 0x74, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x32,
	0x52, 0x0a, 0x69, 0x61, 0x61, 0x73, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x11, 0x78, 0x69,
	0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52,
	0x0f, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x0c,
	0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x32, 0x92, 0x41, 0x1e, 0x32, 0x18, 0xe8, 0x99, 0x9a, 0xe6, 0x8b, 0x9f, 0xe5,
	0xba, 0x94, 0xe7, 0x94, 0xa8, 0x3a, 0x0a, 0x5b, 0x20, 0x6e, 0x6f, 0x20, 0x79, 0x65, 0x73, 0x20,
	0x5d, 0x3a, 0x02, 0x6e, 0x6f, 0xfa, 0x42, 0x0e, 0x72, 0x0c, 0x52, 0x02, 0x6e, 0x6f, 0x52, 0x03,
	0x79, 0x65, 0x73, 0xd0, 0x01, 0x01, 0x52, 0x0c, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x12, 0x5f, 0x0a, 0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x72, 0x74, 0x69,
	0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0x88,
	0xb6, 0xe5, 0x93, 0x81, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69,
	0x66, 0x61, 0x63, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x59, 0x61, 0x6d, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xba, 0x94,
	0xe7, 0x94, 0xa8, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x52, 0x07, 0x61, 0x70, 0x70, 0x59, 0x61,
	0x6d, 0x6c, 0x12, 0x5d, 0x0a, 0x08, 0x61, 0x64, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a,
	0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x41, 0x64, 0x6c, 0x52, 0x65, 0x71, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe5,
	0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe7, 0xba, 0xa7, 0xe5, 0x88, 0xab, 0xe7, 0x9a, 0x84, 0x61, 0x64,
	0x6c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0x52, 0x08, 0x61, 0x64, 0x6c, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x22, 0xf3, 0x08, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x96, 0x01, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x76, 0x92, 0x41, 0x6c, 0x32, 0x6a, 0xe6, 0x96, 0x87,
	0xe4, 0xbb, 0xb6, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0x0a, 0x2d, 0x20, 0xe7, 0xad, 0x89, 0xe4,
	0xba, 0x8e, 0xe4, 0xb8, 0x8a, 0xe7, 0xba, 0xa7, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0xe6,
	0x88, 0x96, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xef, 0xbc, 0x8c, 0xe7, 0x9b,
	0xb8, 0xe5, 0x90, 0x8c, 0xe4, 0xb8, 0x8a, 0xe7, 0xba, 0xa7, 0xe3, 0x80, 0x81, 0xe6, 0x9e, 0xb6,
	0xe6, 0x9e, 0x84, 0xe3, 0x80, 0x81, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0x9a, 0x84, 0xe6,
	0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0xe4, 0xb8, 0x8d, 0xe5, 0xbe,
	0x97, 0xe9, 0x87, 0x8d, 0xe5, 0xa4, 0x8d, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x92, 0x41, 0x0e,
	0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x8f,
	0x01, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x73, 0x92, 0x41, 0x3c, 0x32, 0x3a, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x0a, 0x5b, 0x20, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x20, 0x6f, 0x75,
	0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x20, 0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x20,
	0x72, 0x70, 0x6d, 0x20, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x68, 0x65, 0x6c, 0x6d,
	0x20, 0x5d, 0xfa, 0x42, 0x31, 0x72, 0x2f, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x09,
	0x6f, 0x75, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x03, 0x72, 0x70, 0x6d, 0x52, 0x07,
	0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x52, 0x04, 0x68, 0x65, 0x6c, 0x6d, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0x92, 0x41,
	0x05, 0x32, 0x03, 0x6d, 0x64, 0x35, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x44, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41,
	0x20, 0x32, 0x1e, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe3, 0x80, 0x81, 0xe5, 0x8c, 0x85, 0xe3,
	0x80, 0x81, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0xba, 0x90, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d,
	0x80, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x57, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0x92, 0x41, 0x2a, 0x32,
	0x28, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xad, 0x98, 0xe5, 0x82, 0xa8, 0xe4, 0xbd, 0x8d,
	0xe7, 0xbd, 0xae, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x0a, 0x20, 0x5b, 0x20, 0x68, 0x74, 0x74,
	0x70, 0x20, 0x6d, 0x69, 0x6e, 0x69, 0x6f, 0x20, 0x5d, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x66, 0x69,
	0x6c, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xad, 0x98, 0xe5, 0x82,
	0xa8, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x12, 0x52, 0x0a, 0x07, 0x72, 0x70, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x38, 0x92, 0x41, 0x35, 0x32, 0x33, 0x72, 0x70, 0x6d, 0xe4, 0xbb,
	0x93, 0xe5, 0xba, 0x93, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x0a, 0x20, 0x5b, 0x20, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x20, 0x69, 0x61, 0x61, 0x73, 0x2d, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x20, 0x6f, 0x65, 0x2d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5d, 0x52, 0x07,
	0x72, 0x70, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x70, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x32,
	0x0a, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0x72, 0x65, 0x70, 0x6f, 0x52, 0x09, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x12, 0x2a, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x61, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x32, 0x09, 0xe9,
	0x95, 0x9c, 0xe5, 0x83, 0x8f, 0x74, 0x61, 0x67, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x61, 0x67, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x6b, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x32, 0x0f, 0xe5,
	0x8c, 0x85, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0c,
	0x70, 0x6b, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x0a,
	0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x32, 0x09, 0xe5, 0x8c, 0x85, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac,
	0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6,
	0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xfa, 0x42, 0x21, 0x72, 0x1f,
	0x52, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x75,
	0x6c, 0x65, 0x72, 0x32, 0x32, 0x52, 0x08, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x76, 0x31, 0x30, 0x52,
	0x02, 0x6f, 0x73, 0x12, 0x43, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2f, 0x92, 0x41, 0x19, 0x32, 0x17, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x3a, 0x0a,
	0x5b, 0x20, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x20, 0x5d, 0xfa,
	0x42, 0x10, 0x72, 0x0e, 0x52, 0x05, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x52, 0x05, 0x61, 0x72, 0x6d,
	0x36, 0x34, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x3a, 0x3d, 0x92, 0x41, 0x3a, 0x0a, 0x38, 0xd2,
	0x01, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0xd2, 0x01, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x74, 0x79, 0x70,
	0x65, 0xd2, 0x01, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0xd2, 0x01, 0x04, 0x61, 0x72,
	0x63, 0x68, 0xd2, 0x01, 0x02, 0x6f, 0x73, 0x22, 0xab, 0x07, 0x0a, 0x12, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x37,
	0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xaf, 0xb9, 0xe5,
	0xba, 0x94, 0xe7, 0x9a, 0x84, 0x61, 0x70, 0x70, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x53, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69,
	0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92,
	0x41, 0x19, 0x32, 0x17, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x3a, 0x0a, 0x5b, 0x20, 0x61, 0x6d,
	0x64, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x20, 0x5d, 0xfa, 0x42, 0x10, 0x72, 0x0e,
	0x52, 0x05, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x52, 0x05, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x52, 0x0c,
	0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x39, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0x8f,
	0x8f, 0xe8, 0xbf, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18,
	0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xad, 0x98, 0xe6,
	0x94, 0xbe, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x3f, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32,
	0x12, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x94, 0xaf, 0xe4, 0xb8, 0x80, 0xe6, 0xa0, 0x87,
	0xe8, 0xaf, 0x86, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe4, 0xb8, 0x8a, 0xe4,
	0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe8, 0xb7,
	0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4c,
	0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2c, 0x92, 0x41, 0x29, 0x32, 0x27, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7,
	0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0x3a, 0x0a, 0x5b, 0x20, 0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73, 0x20,
	0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x20, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x5d,
	0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x03,
	0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12,
	0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0x6d, 0x64, 0x35, 0xe5,
	0x80, 0xbc, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x33, 0x0a, 0x08, 0x6f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe8, 0xae, 0xb0, 0xe5,
	0xbd, 0x95, 0x52, 0x08, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41,
	0x17, 0x32, 0x15, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0x9a, 0x84, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x39, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0xa7, 0xe5,
	0x93, 0x81, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x0f, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x20, 0x32, 0x1e, 0xe6, 0x96, 0x87, 0xe4, 0xbb,
	0xb6, 0xe5, 0xad, 0x98, 0xe5, 0x82, 0xa8, 0xe4, 0xbd, 0x8d, 0xe7, 0xbd, 0xae, 0x3a, 0x0a, 0x20,
	0x5b, 0x73, 0x33, 0x20, 0x68, 0x74, 0x74, 0x70, 0x5d, 0x52, 0x0f, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f,
	0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3a, 0x7b, 0x92, 0x41, 0x78, 0x0a, 0x76, 0xd2,
	0x01, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0xd2, 0x01, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0xd2, 0x01, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0xd2, 0x01, 0x03, 0x6d, 0x64, 0x35, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65,
	0x63, 0x74, 0x75, 0x72, 0x65, 0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2,
	0x01, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xf5, 0x06, 0x0a, 0x0a, 0x52, 0x70, 0x6d, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x12, 0x37, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe6, 0x96, 0x87, 0xe4,
	0xbb, 0xb6, 0xe5, 0xaf, 0xb9, 0xe5, 0xba, 0x94, 0xe7, 0x9a, 0x84, 0x61, 0x70, 0x70, 0xe5, 0x90,
	0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x53, 0x0a,
	0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0x41, 0x19, 0x32, 0x17, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84,
	0x3a, 0x0a, 0x5b, 0x20, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x20,
	0x5d, 0xfa, 0x42, 0x10, 0x72, 0x0e, 0x52, 0x05, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x52, 0x05, 0x61,
	0x72, 0x6d, 0x36, 0x34, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75,
	0x72, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x96,
	0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe4, 0xb8, 0x8a, 0xe4,
	0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe8, 0xb7,
	0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe4, 0xb8, 0x8a,
	0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xad, 0x98, 0xe6, 0x94, 0xbe, 0xe8,
	0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x37, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c,
	0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0a, 0x68, 0x6f,
	0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xb8, 0x8a, 0xe4,
	0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0x6d, 0x64, 0x35, 0xe5, 0x80, 0xbc, 0x52, 0x03,
	0x6d, 0x64, 0x35, 0x12, 0x33, 0x0a, 0x08, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x52, 0x08,
	0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0x52,
	0x50, 0x4d, 0xe5, 0x8c, 0x85, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x72, 0x0a, 0x04, 0x72, 0x65, 0x70,
	0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x5e, 0x92, 0x41, 0x5b, 0x32, 0x59, 0xe4, 0xbb,
	0x93, 0xe5, 0xba, 0x93, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x0a, 0x2d, 0x20, 0xe6, 0x96, 0x87,
	0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x5b, 0x20, 0x72, 0x70, 0x6d, 0x20,
	0x5d, 0x20, 0xe5, 0xbf, 0x85, 0xe5, 0xa1, 0xab, 0xe5, 0x8f, 0xaf, 0xe9, 0x80, 0x89, 0x3a, 0x20,
	0x5b, 0x20, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x20, 0x69, 0x61, 0x61, 0x73, 0x2d, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x20, 0x6f, 0x65, 0x2d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x20, 0x2e, 0x2e, 0x2e, 0x20, 0x5d, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x39, 0x0a,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x32, 0x0d,
	0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3a, 0x75, 0x92, 0x41, 0x72, 0x0a, 0x70, 0xd2, 0x01, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0xd2, 0x01, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0xd2, 0x01, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0xd2, 0x01, 0x04, 0x72, 0x65, 0x70, 0x6f, 0xd2, 0x01, 0x03, 0x6d, 0x64, 0x35, 0xd2, 0x01,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65,
	0xd2, 0x01, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc1, 0x06,
	0x0a, 0x15, 0x53, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4,
	0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x53, 0x0a, 0x0c, 0x61, 0x72,
	0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2f, 0x92, 0x41, 0x19, 0x32, 0x17, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x3a, 0x0a, 0x5b,
	0x20, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x20, 0x5d, 0xfa, 0x42,
	0x10, 0x72, 0x0e, 0x52, 0x05, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x52, 0x05, 0x61, 0x72, 0x6d, 0x36,
	0x34, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x39, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x96, 0x87, 0xe4, 0xbb,
	0xb6, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41,
	0x1a, 0x32, 0x18, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5,
	0xad, 0x98, 0xe6, 0x94, 0xbe, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12,
	0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29,
	0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x32, 0x12, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0x6d, 0x64,
	0x35, 0xe5, 0x80, 0xbc, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x37, 0x0a, 0x0a, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90,
	0xa6, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x52, 0x08, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe6, 0xa8, 0xa1, 0xe5,
	0x9d, 0x97, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0x92, 0x41, 0x16, 0x32, 0x14, 0x4a,
	0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x20, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0xe4, 0xbf, 0xa1,
	0xe6, 0x81, 0xaf, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44,
	0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x3a, 0x7b, 0x92, 0x41, 0x78, 0x0a, 0x76, 0xd2, 0x01, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0xd2, 0x01, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0xd2, 0x01, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0xd2, 0x01, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0xd2, 0x01, 0x03, 0x6d,
	0x64, 0x35, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72,
	0x65, 0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xa0, 0x07, 0x0a, 0x12, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c,
	0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x53, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0x41, 0x19,
	0x32, 0x17, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x3a, 0x0a, 0x5b, 0x20, 0x61, 0x6d, 0x64, 0x36,
	0x34, 0x20, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x20, 0x5d, 0xfa, 0x42, 0x10, 0x72, 0x0e, 0x52, 0x05,
	0x61, 0x6d, 0x64, 0x36, 0x34, 0x52, 0x05, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x52, 0x0c, 0x61, 0x72,
	0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0x8f, 0x8f, 0xe8,
	0xbf, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92,
	0x41, 0x1a, 0x32, 0x18, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6,
	0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d,
	0x92, 0x41, 0x1a, 0x32, 0x18, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb,
	0xb6, 0xe5, 0xad, 0x98, 0xe6, 0x94, 0xbe, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x32, 0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x29, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x32, 0x12, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6,
	0x6d, 0x64, 0x35, 0xe5, 0x80, 0xbc, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x33, 0x0a, 0x08, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x32, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96,
	0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x52, 0x08, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x12, 0x31, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x19, 0x92, 0x41, 0x16, 0x32, 0x14, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x20, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xb8, 0x8a, 0xe4,
	0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x12, 0x39, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe6, 0xa8, 0xa1,
	0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x2b,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x32, 0x12, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0x6d,
	0x64, 0x35, 0xe5, 0x80, 0xbc, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41,
	0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3a, 0x88, 0x01, 0x92, 0x41, 0x84, 0x01,
	0x0a, 0x81, 0x01, 0xd2, 0x01, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0xd2, 0x01, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0xd2, 0x01, 0x0e, 0x66,
	0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0xd2, 0x01, 0x08,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0xd2, 0x01, 0x03, 0x74, 0x61, 0x67, 0xd2, 0x01,
	0x04, 0x74, 0x79, 0x70, 0x65, 0xd2, 0x01, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0xd2,
	0x01, 0x03, 0x6d, 0x64, 0x35, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63,
	0x74, 0x75, 0x72, 0x65, 0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x74, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0x92, 0x41, 0x06, 0x32, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x92, 0x41, 0x09,
	0x32, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x3a, 0x16, 0x92, 0x41, 0x13, 0x0a, 0x11, 0xd2, 0x01, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0xd2, 0x01, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x11, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x1d, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09,
	0x92, 0x41, 0x06, 0x32, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x4a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x09, 0x92, 0x41, 0x06, 0x32,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x92, 0x41,
	0x09, 0x32, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x3a, 0x1d, 0x92, 0x41, 0x1a, 0x0a, 0x18, 0xd2, 0x01, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0xd2, 0x01, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0xd2, 0x01, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x22, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x22, 0x29, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x44, 0x22, 0xfd, 0x01, 0x0a, 0x0c, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x96, 0x87, 0xe4,
	0xbb, 0xb6, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe9, 0x87, 0x8d, 0xe8, 0xaf, 0x95, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52,
	0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x30, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x20, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0xd3, 0x01, 0x0a, 0x1b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x1d, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x09, 0x92, 0x41, 0x06, 0x32, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x4e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x09, 0x92, 0x41, 0x06, 0x32, 0x04, 0x64, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x26, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0c, 0x92, 0x41, 0x09, 0x32, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x1d, 0x92, 0x41, 0x1a, 0x0a, 0x18, 0xd2,
	0x01, 0x04, 0x63, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0xd2, 0x01, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x87, 0x03, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x73, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x33, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53,
	0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74,
	0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0x92, 0x41, 0x16, 0x32, 0x14,
	0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x3a, 0x0a, 0x5b, 0x78, 0x38, 0x36, 0x5f, 0x36, 0x34, 0x20,
	0x61, 0x72, 0x6d, 0x5d, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x52, 0x06, 0x78, 0x38, 0x36, 0x5f, 0x36,
	0x34, 0x52, 0x03, 0x61, 0x72, 0x6d, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x6e, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x53, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4e, 0x92, 0x41, 0x29, 0x32, 0x27, 0xe6,
	0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0x3a, 0x0a, 0x5b, 0x20, 0x63,
	0x65, 0x6e, 0x74, 0x6f, 0x73, 0x20, 0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x20,
	0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x5d, 0xfa, 0x42, 0x1f, 0x72, 0x1d, 0x52, 0x06, 0x63, 0x65, 0x6e,
	0x74, 0x6f, 0x73, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x52, 0x05,
	0x6b, 0x79, 0x6c, 0x69, 0x6e, 0xd0, 0x01, 0x01, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x53,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xba, 0x94, 0xe7,
	0x94, 0xa8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x3a, 0x1e, 0x92, 0x41, 0x1b, 0x0a, 0x19, 0xd2, 0x01, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x22, 0xca, 0x05, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x41,
	0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x6f, 0x0a, 0x0b,
	0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63,
	0x74, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe6, 0x99, 0xae, 0xe9, 0x80, 0x9a, 0xe6, 0x96,
	0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0x9b, 0xb8, 0xe5, 0x85, 0xb3, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x52, 0x0b, 0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x59, 0x0a,
	0x03, 0x72, 0x70, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6a, 0x64, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41,
	0x70, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32,
	0x12, 0x52, 0x50, 0x4d, 0xe5, 0x8c, 0x85, 0xe7, 0x9b, 0xb8, 0xe5, 0x85, 0xb3, 0xe4, 0xbf, 0xa1,
	0xe6, 0x81, 0xaf, 0x52, 0x03, 0x72, 0x70, 0x6d, 0x12, 0x77, 0x0a, 0x0c, 0x73, 0x6b, 0x79, 0x77,
	0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x42, 0x23,
	0x92, 0x41, 0x20, 0x32, 0x1e, 0xe4, 0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe9, 0x95, 0x9c, 0xe5, 0x83,
	0x8f, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe7, 0x9b, 0xb8, 0xe5, 0x85, 0xb3, 0xe4, 0xbf, 0xa1,
	0xe6, 0x81, 0xaf, 0x52, 0x0c, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x78, 0x0a, 0x0e, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x32, 0x1b,
	0xe4, 0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe5, 0x8c, 0x85, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe7,
	0x9b, 0xb8, 0xe5, 0x85, 0xb3, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0e, 0x73, 0x6b, 0x79,
	0x77, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x74, 0x0a, 0x0c, 0x78,
	0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63,
	0x74, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x32, 0x1b, 0xe8, 0xa1, 0x8c, 0xe4, 0xba, 0x91, 0xe5, 0x8c,
	0x85, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe7, 0x9b, 0xb8, 0xe5, 0x85, 0xb3, 0xe4, 0xbf, 0xa1,
	0xe6, 0x81, 0xaf, 0x52, 0x0c, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x7b, 0x0a, 0x0e, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x42, 0x23, 0x92, 0x41, 0x20, 0x32, 0x1e,
	0xe8, 0xa1, 0x8c, 0xe4, 0xba, 0x91, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe9, 0x83, 0xa8, 0xe7,
	0xbd, 0xb2, 0xe7, 0x9b, 0xb8, 0xe5, 0x85, 0xb3, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0e,
	0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x22, 0xb2,
	0x02, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x12, 0x2b,
	0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x32, 0x16, 0x61, 0x70, 0x70, 0x20, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x52, 0x0e, 0x61, 0x70, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x37, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe5, 0xba, 0x94, 0xe7, 0x94,
	0xa8, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0e, 0x66, 0x69, 0x6c,
	0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0a, 0x68, 0x6f,
	0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x32, 0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb,
	0x9f, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xcf, 0x0a, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x63, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3f, 0x92, 0x41, 0x3c, 0x32, 0x2a, 0xe9, 0x83, 0xa8,
	0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0x43, 0x50, 0x55, 0xe6,
	0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x20, 0x3a, 0x20, 0x0a, 0x5b, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x2c,
	0x20, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x5d, 0xf2, 0x02, 0x05, 0x61, 0x6d, 0x64, 0x36, 0x34, 0xf2,
	0x02, 0x05, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x51, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0x41, 0x2c, 0x32,
	0x2a, 0xe7, 0x94, 0xa8, 0xe4, 0xba, 0x8e, 0xe7, 0x94, 0x9f, 0xe6, 0x88, 0x90, 0xe5, 0xa2, 0x9e,
	0xe9, 0x87, 0x8f, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe7, 0x9a, 0x84, 0xe5,
	0x9f, 0xba, 0xe5, 0x87, 0x86, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x0b, 0x62, 0x61, 0x73,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x74,
	0x48, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0x92, 0x41,
	0x19, 0x32, 0x17, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5,
	0x99, 0xa8, 0x49, 0x50, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x74,
	0x48, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x12, 0x36, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x32, 0x15, 0xe9,
	0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe5, 0xad, 0x98, 0xe6, 0x94, 0xbe, 0xe8, 0xb7,
	0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3f,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x53, 0x73, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe6, 0x89, 0x93, 0xe5, 0x8c, 0x85,
	0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x99, 0xa8, 0x73, 0x73, 0x68, 0xe7, 0xab, 0xaf, 0xe5,
	0x8f, 0xa3, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x53, 0x73, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x92, 0x01, 0x0a, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x42, 0x70, 0x92, 0x41, 0x44, 0x32, 0x42, 0xe6, 0x93, 0x8d, 0xe4,
	0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x28, 0xe6,
	0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe5, 0xa4, 0x9a, 0xe9, 0x80, 0x89, 0x29, 0x3a, 0x20, 0x0a, 0x5b,
	0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73, 0x2c, 0x20, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x75, 0x6c, 0x65,
	0x72, 0x32, 0x32, 0x2c, 0x20, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x76, 0x31, 0x30, 0x5d, 0xfa, 0x42,
	0x26, 0x92, 0x01, 0x23, 0x22, 0x21, 0x72, 0x1f, 0x52, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73,
	0x52, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x75, 0x6c, 0x65, 0x72, 0x32, 0x32, 0x52, 0x08, 0x6b,
	0x79, 0x6c, 0x69, 0x6e, 0x76, 0x31, 0x30, 0x52, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x32, 0x0f, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x12, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a,
	0x92, 0x41, 0x17, 0x32, 0x15, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe7, 0x89,
	0x88, 0xe6, 0x9c, 0xac, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x12, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x64,
	0x0a, 0x1a, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x24, 0x92, 0x41, 0x21, 0x32, 0x1f, 0xe5, 0xbf, 0xab, 0xe7, 0x85, 0xa7, 0xe7,
	0x94, 0x9f, 0xe6, 0x88, 0x90, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0x2c, 0xe7,
	0xa7, 0x92, 0xe7, 0xba, 0xa7, 0xe5, 0x88, 0xab, 0x52, 0x1a, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x5d, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x41, 0x92, 0x41, 0x3e, 0x32, 0x29, 0xe9, 0x83, 0xa8,
	0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x3a, 0x20, 0x0a, 0x5b, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2c,
	0x20, 0x66, 0x75, 0x6c, 0x6c, 0x5d, 0xf2, 0x02, 0x09, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0xf2, 0x02, 0x04, 0x66, 0x75, 0x6c, 0x6c, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x10, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x33, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x5a,
	0x92, 0x41, 0x57, 0x32, 0x55, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x41, 0x44, 0x4c, 0xe5, 0x8c,
	0x85, 0xe5, 0xad, 0x98, 0xe5, 0x82, 0xa8, 0xe4, 0xbd, 0x8d, 0xe7, 0xbd, 0xae, 0x53, 0x33, 0x2c,
	0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe4, 0xb8, 0x8d, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x2c,
	0xe5, 0x88, 0x99, 0xe8, 0xbf, 0x90, 0xe8, 0xa1, 0x8c, 0xe7, 0x9b, 0xb8, 0xe5, 0xba, 0x94, 0xe5,
	0x91, 0xbd, 0xe4, 0xbb, 0xa4, 0xe4, 0xb8, 0x8b, 0xe8, 0xbd, 0xbd, 0xe5, 0x88, 0xb0, 0xe6, 0x8c,
	0x87, 0xe5, 0xae, 0x9a, 0xe4, 0xbd, 0x8d, 0xe7, 0xbd, 0xae, 0x52, 0x10, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x33, 0x12, 0xa9, 0x01, 0x0a,
	0x1d, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x44, 0x69,
	0x72, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x63, 0x92, 0x41, 0x60, 0x32, 0x5e, 0xe6, 0x98, 0xaf, 0xe5, 0x90,
	0xa6, 0xe5, 0x9c, 0xa8, 0xe8, 0xa7, 0xa6, 0xe5, 0x8f, 0x91, 0xe6, 0x89, 0x93, 0xe5, 0x8c, 0x85,
	0xe5, 0x89, 0x8d, 0xe6, 0xb8, 0x85, 0xe7, 0xa9, 0xba, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe7,
	0x9b, 0xae, 0xe5, 0xbd, 0x95, 0x2c, 0xe7, 0x9b, 0xae, 0xe5, 0x89, 0x8d, 0xe4, 0xbb, 0x85, 0xe6,
	0xb8, 0x85, 0xe7, 0xa9, 0xba, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e,
	0x84, 0xe7, 0x9b, 0xae, 0xe5, 0xbd, 0x95, 0xe5, 0x92, 0x8c, 0xe5, 0x85, 0xac, 0xe5, 0x85, 0xb1,
	0x79, 0x75, 0x6d, 0xe7, 0x9b, 0xae, 0xe5, 0xbd, 0x95, 0x52, 0x1d, 0x63, 0x6c, 0x65, 0x61, 0x72,
	0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x44, 0x69, 0x72, 0x42, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x2d, 0x92, 0x41, 0x2a, 0x32, 0x28, 0xe6, 0x8c, 0x87, 0xe5, 0xae, 0x9a, 0xe4, 0xba, 0xa7, 0xe5,
	0x93, 0x81, 0x28, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x29, 0xe5, 0x87, 0xba, 0xe5,
	0x8c, 0x85, 0xe9, 0xa2, 0x84, 0xe7, 0x95, 0x99, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x52, 0x0d,
	0x73, 0x70, 0x65, 0x63, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x3a, 0x56, 0x92,
	0x41, 0x53, 0x0a, 0x51, 0xd2, 0x01, 0x0a, 0x64, 0x65, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x49,
	0x70, 0xd2, 0x01, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0xd2, 0x01, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0xd2, 0x01, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69,
	0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0xd2, 0x01, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xfb, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x32, 0x0f, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75,
	0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x32, 0x09, 0x43,
	0x50, 0x55, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74,
	0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x32,
	0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x3a,
	0x32, 0x92, 0x41, 0x2f, 0x0a, 0x2d, 0xd2, 0x01, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74,
	0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0xd2, 0x01, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2d, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0xe4, 0xbf,
	0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x1d,
	0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x32, 0x06, 0xe6, 0x97, 0xa5, 0xe5, 0xbf, 0x97, 0x52, 0x03, 0x6c, 0x6f, 0x67, 0x22, 0x84, 0x04,
	0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x51, 0x0a, 0x0b, 0x62, 0x61,
	0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2f, 0x92, 0x41, 0x2c, 0x32, 0x2a, 0xe7, 0x94, 0xa8, 0xe4, 0xba, 0x8e, 0xe7, 0x94, 0x9f, 0xe6,
	0x88, 0x90, 0xe5, 0xa2, 0x9e, 0xe9, 0x87, 0x8f, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c,
	0x85, 0xe7, 0x9a, 0x84, 0xe5, 0x9f, 0xba, 0xe5, 0x87, 0x86, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac,
	0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a,
	0x0a, 0x64, 0x65, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1c, 0x92, 0x41, 0x19, 0x32, 0x17, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe6, 0x9c,
	0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x99, 0xa8, 0x49, 0x50, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52,
	0x0a, 0x64, 0x65, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x12, 0x36, 0x0a, 0x08, 0x64,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92,
	0x41, 0x17, 0x32, 0x15, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe5, 0xad, 0x98,
	0xe6, 0x94, 0xbe, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0x52, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x3f, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x53, 0x73, 0x68, 0x50, 0x6f,
	0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe6,
	0x89, 0x93, 0xe5, 0x8c, 0x85, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x99, 0xa8, 0x73, 0x73,
	0x68, 0xe7, 0xab, 0xaf, 0xe5, 0x8f, 0xa3, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x53, 0x73, 0x68,
	0x50, 0x6f, 0x72, 0x74, 0x12, 0x3c, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x32, 0x0f, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0x92, 0x41, 0x2a, 0x32, 0x15, 0xe9, 0x83, 0xa8, 0xe7, 0xbd,
	0xb2, 0xe5, 0x8c, 0x85, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0xf2, 0x02, 0x09, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0xf2, 0x02, 0x04, 0x66,
	0x75, 0x6c, 0x6c, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x3a, 0x55, 0x92,
	0x41, 0x52, 0x0a, 0x50, 0xd2, 0x01, 0x0a, 0x64, 0x65, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x49,
	0x70, 0xd2, 0x01, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0xd2, 0x01, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0xd2, 0x01, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0xd2, 0x01, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69,
	0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0xd2, 0x01, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xf8, 0x04, 0x0a, 0x0f, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x74,
	0x48, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0x92, 0x41,
	0x19, 0x32, 0x17, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5,
	0x99, 0xa8, 0x49, 0x50, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x00, 0x18, 0x40, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x12,
	0x3f, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x23, 0x92, 0x41, 0x17, 0x32, 0x15, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c,
	0x85, 0xe5, 0xad, 0x98, 0xe6, 0x94, 0xbe, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x45, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x11, 0x32, 0x0f, 0xe9,
	0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x53,
	0x73, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41,
	0x1a, 0x32, 0x18, 0xe6, 0x89, 0x93, 0xe5, 0x8c, 0x85, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5,
	0x99, 0xa8, 0x73, 0x73, 0x68, 0xe7, 0xab, 0xaf, 0xe5, 0x8f, 0xa3, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x74, 0x53, 0x73, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x00, 0x18, 0x80, 0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0x92, 0x41,
	0x14, 0x32, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xe4,
	0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0x80, 0x01, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68,
	0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1b,
	0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x0c, 0x61, 0x72, 0x63,
	0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x68, 0x6f, 0x73,
	0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92,
	0x41, 0x14, 0x32, 0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52,
	0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x20, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x20, 0x74,
	0x69, 0x6d, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0xa4, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3c,
	0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x32, 0x0f, 0xe9, 0x83, 0xa8,
	0xe7, 0xbd, 0xb2, 0xe5, 0x8c, 0x85, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x0e, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x20, 0x74, 0x69,
	0x6d, 0x65, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x3a, 0x16,
	0x92, 0x41, 0x13, 0x0a, 0x11, 0xd2, 0x01, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc9, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x53, 0x43, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x09, 0x92, 0x41, 0x06, 0x32, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x5d, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x43, 0x46, 0x69,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x09, 0x92, 0x41, 0x06, 0x32, 0x04, 0x64, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x30, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x16, 0x92, 0x41, 0x09, 0x32, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0x80, 0x08, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0xf0, 0x04, 0x0a, 0x1b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53,
	0x43, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x3a, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47,
	0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x17, 0x32, 0x15, 0xe6, 0x96, 0x87, 0xe4, 0xbb,
	0xb6, 0xe7, 0x9a, 0x84, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69,
	0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d,
	0x32, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x20, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x68, 0x6f, 0x73,
	0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92,
	0x41, 0x14, 0x32, 0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52,
	0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0x92, 0x41,
	0x14, 0x32, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xe4,
	0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0x80, 0x01, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x17, 0x32, 0x15, 0xe6,
	0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0x9a, 0x84, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0xe4, 0xbf,
	0xa1, 0xe6, 0x81, 0xaf, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x14, 0x32,
	0x12, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x63,
	0x6f, 0x64, 0x65, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4,
	0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x00, 0x18, 0x80, 0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x10, 0x92, 0x41, 0x0d, 0x32, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x20, 0x74, 0x69,
	0x6d, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1b, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe7, 0x89, 0x88,
	0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x40, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x96, 0x03, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x43, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x42,
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x14, 0x32, 0x12, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x63, 0x6f, 0x64, 0x65, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x0e, 0x32, 0x0c,
	0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a,
	0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x17, 0x32, 0x15, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6,
	0xe7, 0x9a, 0x84, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x0c, 0x61, 0x72, 0x63, 0x68, 0x69, 0x74,
	0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x59, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0x92, 0x41, 0x14, 0x32,
	0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0xfa, 0x42, 0x1f, 0x72, 0x1d, 0x52, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73,
	0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x52, 0x05, 0x6b, 0x79, 0x6c,
	0x69, 0x6e, 0xd0, 0x01, 0x01, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa5,
	0x06, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x12, 0x35, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x0d, 0x32, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x0c, 0x32, 0x0a, 0xe4, 0xba, 0xa7, 0xe5,
	0x93, 0x81, 0x43, 0x6f, 0x64, 0x65, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x70,
	0x72, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x32, 0x0a, 0xe5, 0xba,
	0x94, 0xe7, 0x94, 0xa8, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x37, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x32, 0x0e, 0xe5, 0xba,
	0x94, 0xe7, 0x94, 0xa8, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x69, 0x64, 0x52, 0x0c, 0x61, 0x70,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0xc7, 0x02, 0x0a, 0x0a, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0xa6, 0x02, 0x92, 0x41, 0x92, 0x01, 0x32, 0x8f, 0x01, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe6,
	0xa8, 0xa1, 0xe5, 0xbc, 0x8f, 0x3a, 0x0a, 0x5b, 0x20, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x20, 0x73, 0x6b, 0x79, 0x77, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x20, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x20,
	0x68, 0x65, 0x6c, 0x6d, 0x20, 0x72, 0x70, 0x6d, 0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x69,
	0x6e, 0x20, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x32, 0x20, 0x69, 0x61, 0x61, 0x73, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x20, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x20, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x20, 0x5d, 0xfa, 0x42, 0x8c, 0x01, 0x72, 0x89, 0x01, 0x52,
	0x11, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x52, 0x0f, 0x73, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x52, 0x0e, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x52, 0x05, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x52, 0x04, 0x68, 0x65, 0x6c, 0x6d,
	0x52, 0x03, 0x72, 0x70, 0x6d, 0x52, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x52,
	0x07, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x32, 0x52, 0x0a, 0x69, 0x61, 0x61, 0x73, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x11, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0f, 0x78, 0x69, 0x6e, 0x67, 0x79, 0x75, 0x6e,
	0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x65, 0x6e, 0x76, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe7, 0x8e, 0xaf, 0xe5,
	0xa2, 0x83, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x68, 0x0a, 0x0d,
	0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65,
	0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x46, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe5, 0x88, 0xb6, 0xe5, 0x93,
	0x81, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0xfe, 0x06, 0x0a, 0x0c, 0x70, 0x75, 0x73, 0x68, 0x46,
	0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x8f, 0x01, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x73, 0x92, 0x41, 0x3c, 0x32,
	0x3a, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x0a, 0x5b,
	0x20, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x20, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x20, 0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x20, 0x72, 0x70, 0x6d, 0x20, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x20, 0x68, 0x65, 0x6c, 0x6d, 0x20, 0x5d, 0xfa, 0x42, 0x31, 0x72, 0x2f,
	0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x03, 0x72, 0x70, 0x6d, 0x52, 0x07, 0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x04, 0x68, 0x65, 0x6c, 0x6d, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0x92, 0x41, 0x05, 0x32, 0x03, 0x6d, 0x64, 0x35, 0x52,
	0x03, 0x6d, 0x64, 0x35, 0x12, 0x43, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2f, 0x92, 0x41, 0x19, 0x32, 0x17, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84, 0x3a,
	0x0a, 0x5b, 0x20, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x36, 0x34, 0x20, 0x5d,
	0xfa, 0x42, 0x10, 0x72, 0x0e, 0x52, 0x05, 0x61, 0x6d, 0x64, 0x36, 0x34, 0x52, 0x05, 0x61, 0x72,
	0x6d, 0x36, 0x34, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x45, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x93, 0x8d, 0xe4,
	0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xfa, 0x42, 0x21, 0x72, 0x1f, 0x52, 0x06, 0x63,
	0x65, 0x6e, 0x74, 0x6f, 0x73, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x75, 0x6c, 0x65, 0x72,
	0x32, 0x32, 0x52, 0x08, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x76, 0x31, 0x30, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x57, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0x92, 0x41, 0x2a, 0x32, 0x28,
	0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xad, 0x98, 0xe5, 0x82, 0xa8, 0xe4, 0xbd, 0x8d, 0xe7,
	0xbd, 0xae, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x0a, 0x20, 0x5b, 0x20, 0x68, 0x74, 0x74, 0x70,
	0x20, 0x6d, 0x69, 0x6e, 0x69, 0x6f, 0x20, 0x5d, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x66, 0x69, 0x6c,
	0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x32, 0x12, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xad, 0x98, 0xe5, 0x82, 0xa8,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x52, 0x0a, 0x07, 0x72, 0x70, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x38, 0x92, 0x41, 0x35, 0x32, 0x33, 0x72, 0x70, 0x6d, 0xe4, 0xbb, 0x93,
	0xe5, 0xba, 0x93, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x0a, 0x20, 0x5b, 0x20, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x20, 0x69, 0x61, 0x61, 0x73, 0x2d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x20, 0x6f, 0x65, 0x2d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5d, 0x52, 0x07, 0x72,
	0x70, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x70, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x32, 0x0a,
	0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0x72, 0x65, 0x70, 0x6f, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x12, 0x2a, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x32, 0x09, 0xe9, 0x95,
	0x9c, 0xe5, 0x83, 0x8f, 0x74, 0x61, 0x67, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61,
	0x67, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x6b, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x32, 0x0f, 0xe5, 0x8c,
	0x85, 0xe6, 0xa8, 0xa1, 0xe5, 0x9d, 0x97, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0c, 0x70,
	0x6b, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x70,
	0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0e, 0x92, 0x41, 0x0b, 0x32, 0x09, 0xe5, 0x8c, 0x85, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52,
	0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x08, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86,
	0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x32, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x93, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2d, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0xe4, 0xbf,
	0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x1d,
	0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x32, 0x06, 0xe6, 0x97, 0xa5, 0xe5, 0xbf, 0x97, 0x52, 0x03, 0x6c, 0x6f, 0x67, 0x22, 0x89, 0x07,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x0f, 0x32, 0x0d, 0x4a, 0x44, 0x53, 0x74, 0x61,
	0x63, 0x6b, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x40, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x07, 0x61, 0x70,
	0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c,
	0x32, 0x0a, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x41, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2d, 0x92, 0x41, 0x18, 0x32, 0x16, 0xe6, 0x9e, 0xb6, 0xe6, 0x9e, 0x84,
	0x3a, 0x0a, 0x5b, 0x20, 0x78, 0x38, 0x36, 0x5f, 0x36, 0x34, 0x20, 0x61, 0x72, 0x6d, 0x20, 0x5d,
	0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x52, 0x06, 0x78, 0x38, 0x36, 0x5f, 0x36, 0x34, 0x52, 0x03, 0x61,
	0x72, 0x6d, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x82, 0x01, 0x0a, 0x0a, 0x68, 0x6f, 0x73,
	0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x62, 0x92,
	0x41, 0x40, 0x32, 0x3e, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f,
	0xe5, 0x8f, 0x91, 0xe8, 0xa1, 0x8c, 0xe7, 0x89, 0x88, 0xef, 0xbc, 0x9a, 0x48, 0x6f, 0x73, 0x74,
	0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x6f, 0x73, 0xe3, 0x80, 0x81, 0x48,
	0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c,
	0x65, 0x72, 0xfa, 0x42, 0x1c, 0x72, 0x1a, 0x52, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x6f, 0x73, 0x52,
	0x09, 0x6f, 0x70, 0x65, 0x6e, 0x45, 0x75, 0x6c, 0x65, 0x72, 0x52, 0x05, 0x6b, 0x79, 0x6c, 0x69,
	0x6e, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x32, 0x15, 0xe6, 0x96, 0x87, 0xe4,
	0xbb, 0xb6, 0xe6, 0x88, 0x96, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe6, 0xa0, 0x87, 0xe8, 0xaf,
	0x86, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x12, 0xd3, 0x01, 0x0a, 0x0d, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53,
	0x70, 0x65, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0xac, 0x01, 0x92, 0x41, 0x5d, 0x32,
	0x5b, 0xe7, 0x9b, 0xae, 0xe5, 0xbd, 0x95, 0xe5, 0x91, 0xbd, 0xe5, 0x90, 0x8d, 0xe8, 0xa7, 0x84,
	0xe8, 0x8c, 0x83, 0xef, 0xbc, 0x9a, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53,
	0x70, 0x65, 0x63, 0x41, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0xe3, 0x80, 0x81, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x70, 0x65, 0x63, 0x41, 0x72, 0x63, 0x68, 0x53, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0xfa, 0x42, 0x49, 0x72,
	0x47, 0x52, 0x24, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x70, 0x65, 0x63,
	0x41, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x1f, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x53, 0x70, 0x65, 0x63, 0x41, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x0d, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x53, 0x70, 0x65, 0x63, 0x12, 0x36, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x32, 0x15,
	0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0x88, 0x96, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x32, 0x0f, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0xa0,
	0xa1, 0xe9, 0xaa, 0x8c, 0xe7, 0xa0, 0x81, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x34, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x18, 0x92, 0x41, 0x15, 0x32, 0x13, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6,
	0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x2f, 0xe5, 0xad, 0x97, 0xe8, 0x8a, 0x82, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x54, 0x61, 0x67, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x32,
	0x15, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0x88, 0x96, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f,
	0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67,
	0x73, 0x12, 0x33, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x32, 0x0c, 0xe6, 0x96, 0x87,
	0xe4, 0xbb, 0xb6, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x05, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x32, 0x06, 0xe5, 0x8f, 0x82, 0xe6,
	0x95, 0xb0, 0x52, 0x05, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x22, 0x7c, 0x0a, 0x19, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1c, 0x92, 0x41, 0x19, 0x32, 0x17, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe6,
	0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x99, 0xa8, 0x49, 0x50, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x31, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x32, 0x18, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe8,
	0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0xef, 0xbc, 0x88, 0xe9, 0xa2, 0x84, 0xe7, 0x95, 0x99, 0xef, 0xbc,
	0x89, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x32, 0x8a, 0x2e, 0x0a, 0x11, 0x4a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0xbe, 0x01,
	0x0a, 0x1c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x3c, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x1d, 0x1a, 0x1b, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x99, 0xa8,
	0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x66,
	0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0xe2,
	0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x38, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65,
	0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x58, 0x92, 0x41, 0x36, 0x1a, 0x34,
	0xe4, 0xb8, 0x8b, 0xe8, 0xbd, 0xbd, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x88, 0xb0, 0xe5,
	0x85, 0xac, 0xe5, 0x85, 0xb1, 0xe7, 0x9b, 0xae, 0xe5, 0xbd, 0x95, 0xef, 0xbc, 0x88, 0xe5, 0xbc,
	0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb,
	0xba, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x12, 0xe8, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3c, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a,
	0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x5a, 0x92, 0x41, 0x36, 0x1a, 0x34, 0xe4, 0xb8, 0x8b, 0xe8, 0xbd, 0xbd, 0xe6,
	0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x88, 0xb0, 0xe5, 0x85, 0xac, 0xe5, 0x85, 0xb1, 0xe7, 0x9b,
	0xae, 0xe5, 0xbd, 0x95, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5,
	0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x3a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0xeb,
	0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x70, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x67, 0x92, 0x41, 0x42, 0x1a, 0x40, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81,
	0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5, 0x88, 0xb6, 0xe5,
	0x93, 0x81, 0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba,
	0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3,
	0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c,
	0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x3a, 0x70, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x12, 0xfc, 0x01, 0x0a,
	0x18, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3b, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x6f, 0x92, 0x41, 0x48, 0x1a,
	0x46, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe5, 0xba, 0x94,
	0xe7, 0x94, 0xa8, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5,
	0x88, 0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc,
	0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f,
	0xa5, 0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x9f, 0x02, 0x0a, 0x1f,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x41, 0x6e,
	0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x65, 0x61, 0x6c, 0x12,
	0x45, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61,
	0x63, 0x74, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53,
	0x65, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x49, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x65, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x6a, 0x92, 0x41, 0x32, 0x1a, 0x30, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe5, 0xb0,
	0x81, 0xe6, 0x9d, 0xbf, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81,
	0xe5, 0x92, 0x8c, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5,
	0x85, 0x83, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01,
	0x2a, 0x22, 0x2a, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x3a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x53, 0x65, 0x61, 0x6c, 0x12, 0x9f, 0x02,
	0x0a, 0x21, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74,
	0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x79, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x12, 0x47, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65,
	0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x42, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x49, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63,
	0x74, 0x41, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x65,
	0x61, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x66, 0x92, 0x41, 0x2c, 0x1a, 0x2a, 0xe6, 0xa0,
	0xb9, 0xe6, 0x8d, 0xae, 0xe9, 0x83, 0xa8, 0xe7, 0xbd, 0xb2, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0,
	0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x92, 0x8c, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe5,
	0x85, 0x83, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01,
	0x2a, 0x22, 0x2c, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x3a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x12,
	0xe7, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x41, 0x72, 0x74,
	0x69, 0x66, 0x61, 0x63, 0x74, 0x12, 0x37, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46,
	0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x73, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x39,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x73, 0x41, 0x72, 0x74, 0x69,
	0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5e, 0x92, 0x41, 0x29, 0x1a, 0x27,
	0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0xe5, 0x92, 0x8c, 0xe5,
	0xba, 0x94, 0xe7, 0x94, 0xa8, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe5, 0x88, 0xb6, 0xe5, 0x93,
	0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a,
	0x22, 0x27, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a,
	0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x12, 0x88, 0x02, 0x0a, 0x19, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x3b, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a,
	0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x78, 0x92, 0x41, 0x30, 0x1a,
	0x2e, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe7, 0xa6, 0xbb,
	0xe7, 0xba, 0xbf, 0xe5, 0x8c, 0x85, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6,
	0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xef, 0xbc, 0x89, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x3a, 0x01, 0x2a, 0x22, 0x3a, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x2f, 0x7b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x65,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x12, 0xef, 0x01, 0x0a, 0x1b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a,
	0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3f, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5b, 0x92, 0x41, 0x30, 0x1a, 0x2e,
	0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe7, 0xa6, 0xbb, 0xe7,
	0xba, 0xbf, 0xe5, 0x8c, 0x85, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e,
	0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x3a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0xeb, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x43, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x37, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x43, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x63, 0x92, 0x41, 0x3e, 0x1a, 0x3c, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0x73, 0x63, 0xe6, 0x89,
	0x80, 0xe6, 0x9c, 0x89, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf,
	0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6,
	0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xef,
	0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x66, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x70, 0x75, 0x73, 0x68, 0x41, 0x6c, 0x6c,
	0x46, 0x69, 0x6c, 0x65, 0x12, 0xfa, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x53, 0x43, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a,
	0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x53, 0x43, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x6b, 0x92, 0x41, 0x44, 0x1a, 0x42, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf,
	0xa2, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0x73, 0x63, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5,
	0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbb,
	0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5,
	0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x3a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x75, 0x73, 0x68, 0x41, 0x6c, 0x6c, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0xcf, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x36, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x34,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x4d, 0x92, 0x41, 0x17, 0x1a, 0x15, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc,
	0xa0, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe6, 0x88, 0x96, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0xc8, 0x01, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x12, 0x34, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x14, 0x1a, 0x12, 0xe5, 0x88, 0x9b, 0xe5, 0xbb,
	0xba, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x7b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x12, 0xf7,
	0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x46, 0x69, 0x6c, 0x65, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x35, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65,
	0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x6b, 0x92, 0x41, 0x42,
	0x1a, 0x40, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe6, 0x99, 0xae, 0xe9, 0x80, 0x9a, 0xe6, 0x96,
	0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf,
	0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6,
	0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xef,
	0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x66, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x70, 0x75, 0x73, 0x68, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x12, 0xd6, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x70, 0x6d, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2d,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x52, 0x70, 0x6d, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5a, 0x92, 0x41, 0x39, 0x1a, 0x37, 0xe6, 0x8e, 0xa8, 0xe9,
	0x80, 0x81, 0x72, 0x70, 0x6d, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0xb0, 0xe7, 0x8e,
	0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82,
	0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba,
	0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x66,
	0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x70, 0x75, 0x73, 0x68, 0x52, 0x70,
	0x6d, 0x12, 0xfd, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x6b, 0x79, 0x77,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x38, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x53, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a,
	0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x6b, 0x92, 0x41, 0x3f, 0x1a, 0x3d, 0xe6, 0x8e, 0xa8, 0xe9, 0x80,
	0x81, 0xe4, 0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe5, 0x8c, 0x85, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81,
	0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef,
	0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5,
	0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01,
	0x2a, 0x22, 0x1e, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x70,
	0x75, 0x73, 0x68, 0x53, 0x6b, 0x79, 0x77, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x12, 0xf1, 0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x63, 0x6b,
	0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x35, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x65,
	0x92, 0x41, 0x3c, 0x1a, 0x3a, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0x64, 0x6f, 0x63, 0x6b, 0x65,
	0x72, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83,
	0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6,
	0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xef, 0xbc, 0x89, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x70, 0x75, 0x73, 0x68, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x86, 0x02, 0x0a, 0x1b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46,
	0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3e, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x73, 0x92, 0x41, 0x48, 0x1a, 0x46,
	0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe6, 0x99, 0xae, 0xe9,
	0x80, 0x9a, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x88,
	0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88,
	0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5,
	0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x66,
	0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50,
	0x75, 0x73, 0x68, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x12, 0xed,
	0x01, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x70, 0x6d, 0x50, 0x75,
	0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3e, 0x2e, 0x6a, 0x64, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x46, 0x69, 0x6c,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x62, 0x92, 0x41, 0x3f, 0x1a,
	0x3d, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0x72, 0x70, 0x6d,
	0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4,
	0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e,
	0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x3a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x75, 0x73, 0x68, 0x52, 0x70, 0x6d, 0x12, 0x89,
	0x02, 0x0a, 0x1e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x6b, 0x79, 0x77, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3e, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46,
	0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x73, 0x92, 0x41, 0x45, 0x1a, 0x43, 0xe6, 0x9f, 0xa5, 0xe8,
	0xaf, 0xa2, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe4, 0xba, 0x91, 0xe7, 0xbf, 0xbc, 0xe5, 0x8c,
	0x85, 0xe5, 0x88, 0xb6, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0xb0, 0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83,
	0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6,
	0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x3a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x75, 0x73, 0x68, 0x53, 0x6b, 0x79, 0x77,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x80, 0x02, 0x0a, 0x1b, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x50, 0x75, 0x73, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3e,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x6d,
	0x92, 0x41, 0x42, 0x1a, 0x40, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x8e, 0xa8, 0xe9, 0x80,
	0x81, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0xe9, 0x95, 0x9c, 0xe5, 0x83, 0x8f, 0xe5, 0x88, 0xb0,
	0xe7, 0x8e, 0xaf, 0xe5, 0xa2, 0x83, 0xe4, 0xbb, 0x93, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x88, 0xe5,
	0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5, 0xe8,
	0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x66, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x75,
	0x73, 0x68, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x86, 0x02,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a,
	0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69,
	0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x78, 0x92, 0x41,
	0x30, 0x1a, 0x2e, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0xe5, 0xb0, 0x81, 0xe6, 0x9d, 0xbf, 0xe5,
	0x85, 0x83, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad,
	0xa5, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x2d, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xef, 0xbc,
	0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x3a, 0x01, 0x2a, 0x22, 0x3a, 0x2f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x3a, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0xeb, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x12, 0x32, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x7a, 0x65, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3f, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x7a, 0x65, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5b, 0x92, 0x41, 0x30, 0x1a, 0x2e, 0xe5, 0xaf,
	0xbc, 0xe5, 0x87, 0xba, 0xe5, 0xb0, 0x81, 0xe6, 0x9d, 0xbf, 0xe5, 0x85, 0x83, 0xe6, 0x95, 0xb0,
	0xe6, 0x8d, 0xae, 0xef, 0xbc, 0x88, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe6, 0x8e, 0xa5, 0xe5,
	0x8f, 0xa3, 0x2d, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xef, 0xbc, 0x89, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x22, 0x12, 0x20, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x3a,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x42, 0xa3, 0x01, 0x92, 0x41, 0x46, 0x12, 0x2b, 0x0a, 0x25, 0x4a, 0x44,
	0x53, 0x74, 0x61, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f,
	0x4a, 0x44, 0x53, 0x74, 0x61, 0x63, 0x6b, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0xae, 0xa1,
	0xe7, 0x90, 0x86, 0x32, 0x02, 0x76, 0x31, 0x1a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x68, 0x6f,
	0x73, 0x74, 0x3a, 0x33, 0x35, 0x37, 0x31, 0x31, 0x22, 0x03, 0x2f, 0x76, 0x31, 0x2a, 0x01, 0x01,
	0x5a, 0x58, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6a, 0x64, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x66, 0x61, 0x62, 0x72, 0x69, 0x63, 0x2f, 0x7a, 0x65, 0x75, 0x73, 0x56, 0x32, 0x2f, 0x7a, 0x65,
	0x75, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x62,
	0x2f, 0x6a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x62, 0x4a, 0x64, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x46, 0x69, 0x6c, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_jdstack_filecenter_proto_rawDescOnce sync.Once
	file_jdstack_filecenter_proto_rawDescData = file_jdstack_filecenter_proto_rawDesc
)

func file_jdstack_filecenter_proto_rawDescGZIP() []byte {
	file_jdstack_filecenter_proto_rawDescOnce.Do(func() {
		file_jdstack_filecenter_proto_rawDescData = protoimpl.X.CompressGZIP(file_jdstack_filecenter_proto_rawDescData)
	})
	return file_jdstack_filecenter_proto_rawDescData
}

var file_jdstack_filecenter_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_jdstack_filecenter_proto_goTypes = []interface{}{
	(*FilePropertiesRes)(nil),                      // 0: jdstack.zeus.v1.jdstackFilecenter.FilePropertiesRes
	(*CreateUploadTaskReq)(nil),                    // 1: jdstack.zeus.v1.jdstackFilecenter.CreateUploadTaskReq
	(*CreateFileDownloadReq)(nil),                  // 2: jdstack.zeus.v1.jdstackFilecenter.CreateFileDownloadReq
	(*DescribeFileDownloadReply)(nil),              // 3: jdstack.zeus.v1.jdstackFilecenter.DescribeFileDownloadReply
	(*ModifyArtifactAndMetadataByDeployReq)(nil),   // 4: jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataByDeployReq
	(*ModifyArtifactAndMetadataBySealReq)(nil),     // 5: jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataBySealReq
	(*DescribeArtifactAndMetadataBySealReply)(nil), // 6: jdstack.zeus.v1.jdstackFilecenter.DescribeArtifactAndMetadataBySealReply
	(*ApplicationInfoReq)(nil),                     // 7: jdstack.zeus.v1.jdstackFilecenter.ApplicationInfoReq
	(*ArtifactReq)(nil),                            // 8: jdstack.zeus.v1.jdstackFilecenter.ArtifactReq
	(*RegularFileTaskReq)(nil),                     // 9: jdstack.zeus.v1.jdstackFilecenter.RegularFileTaskReq
	(*RpmTaskReq)(nil),                             // 10: jdstack.zeus.v1.jdstackFilecenter.RpmTaskReq
	(*SkywingPackageTaskReq)(nil),                  // 11: jdstack.zeus.v1.jdstackFilecenter.SkywingPackageTaskReq
	(*DockerImageTaskReq)(nil),                     // 12: jdstack.zeus.v1.jdstackFilecenter.DockerImageTaskReq
	(*DescribeCommonReply)(nil),                    // 13: jdstack.zeus.v1.jdstackFilecenter.DescribeCommonReply
	(*DescribeTaskReply)(nil),                      // 14: jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReply
	(*TaskInfo)(nil),                               // 15: jdstack.zeus.v1.jdstackFilecenter.taskInfo
	(*DescribeTaskReq)(nil),                        // 16: jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	(*PushTaskInfo)(nil),                           // 17: jdstack.zeus.v1.jdstackFilecenter.PushTaskInfo
	(*DescribeCommonFileTaskReply)(nil),            // 18: jdstack.zeus.v1.jdstackFilecenter.DescribeCommonFileTaskReply
	(*DescribesArtifactReq)(nil),                   // 19: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReq
	(*DescribesArtifactReply)(nil),                 // 20: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply
	(*AppArtifact)(nil),                            // 21: jdstack.zeus.v1.jdstackFilecenter.AppArtifact
	(*CreateArtifactPackageReq)(nil),               // 22: jdstack.zeus.v1.jdstackFilecenter.CreateArtifactPackageReq
	(*DescribeArtifactPackageReq)(nil),             // 23: jdstack.zeus.v1.jdstackFilecenter.DescribeArtifactPackageReq
	(*DescribeArtifactPackageReply)(nil),           // 24: jdstack.zeus.v1.jdstackFilecenter.DescribeArtifactPackageReply
	(*CreateMetadataPackageReq)(nil),               // 25: jdstack.zeus.v1.jdstackFilecenter.CreateMetadataPackageReq
	(*PackageTaskInfo)(nil),                        // 26: jdstack.zeus.v1.jdstackFilecenter.PackageTaskInfo
	(*DescribeMetadataPackageReq)(nil),             // 27: jdstack.zeus.v1.jdstackFilecenter.DescribeMetadataPackageReq
	(*DescribeSCFileTaskReply)(nil),                // 28: jdstack.zeus.v1.jdstackFilecenter.DescribeSCFileTaskReply
	(*DescribeSCFilesResponseData)(nil),            // 29: jdstack.zeus.v1.jdstackFilecenter.DescribeSCFilesResponseData
	(*CreateSCFilesTaskReq)(nil),                   // 30: jdstack.zeus.v1.jdstackFilecenter.CreateSCFilesTaskReq
	(*AppFilesTaskReq)(nil),                        // 31: jdstack.zeus.v1.jdstackFilecenter.AppFilesTaskReq
	(*PushFileInfo)(nil),                           // 32: jdstack.zeus.v1.jdstackFilecenter.pushFileInfo
	(*DescribeAppFileTaskReply)(nil),               // 33: jdstack.zeus.v1.jdstackFilecenter.DescribeAppFileTaskReply
	(*CreateArtifactReq)(nil),                      // 34: jdstack.zeus.v1.jdstackFilecenter.CreateArtifactReq
	(*DeploymentServerInfoReply)(nil),              // 35: jdstack.zeus.v1.jdstackFilecenter.DeploymentServerInfoReply
	(*v1.AdlReq)(nil),                              // 36: jdstack.zeus.v1.goodsStack.AdlReq
	(*v11.CreateReq)(nil),                          // 37: jdstack.zeus.v1.unifiedMetadata.CreateReq
	(*empty.Empty)(nil),                            // 38: google.protobuf.Empty
	(*v1.DescribesFilecenterReply)(nil),            // 39: jdstack.zeus.v1.goodsStack.DescribesFilecenterReply
}
var file_jdstack_filecenter_proto_depIdxs = []int32{
	0,  // 0: jdstack.zeus.v1.jdstackFilecenter.CreateUploadTaskReq.files:type_name -> jdstack.zeus.v1.jdstackFilecenter.FilePropertiesRes
	7,  // 1: jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataByDeployReq.appInfo:type_name -> jdstack.zeus.v1.jdstackFilecenter.ApplicationInfoReq
	7,  // 2: jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataBySealReq.appInfos:type_name -> jdstack.zeus.v1.jdstackFilecenter.ApplicationInfoReq
	36, // 3: jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataBySealReq.adlFiles:type_name -> jdstack.zeus.v1.goodsStack.AdlReq
	37, // 4: jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataBySealReq.businessMetadata:type_name -> jdstack.zeus.v1.unifiedMetadata.CreateReq
	8,  // 5: jdstack.zeus.v1.jdstackFilecenter.ApplicationInfoReq.artifacts:type_name -> jdstack.zeus.v1.jdstackFilecenter.ArtifactReq
	36, // 6: jdstack.zeus.v1.jdstackFilecenter.ApplicationInfoReq.adlFiles:type_name -> jdstack.zeus.v1.goodsStack.AdlReq
	15, // 7: jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReply.data:type_name -> jdstack.zeus.v1.jdstackFilecenter.taskInfo
	17, // 8: jdstack.zeus.v1.jdstackFilecenter.DescribeCommonFileTaskReply.data:type_name -> jdstack.zeus.v1.jdstackFilecenter.PushTaskInfo
	21, // 9: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply.regularFile:type_name -> jdstack.zeus.v1.jdstackFilecenter.AppArtifact
	21, // 10: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply.rpm:type_name -> jdstack.zeus.v1.jdstackFilecenter.AppArtifact
	21, // 11: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply.skywingImage:type_name -> jdstack.zeus.v1.jdstackFilecenter.AppArtifact
	21, // 12: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply.skywingPackage:type_name -> jdstack.zeus.v1.jdstackFilecenter.AppArtifact
	21, // 13: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply.xingyunImage:type_name -> jdstack.zeus.v1.jdstackFilecenter.AppArtifact
	21, // 14: jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply.xingyunPackage:type_name -> jdstack.zeus.v1.jdstackFilecenter.AppArtifact
	29, // 15: jdstack.zeus.v1.jdstackFilecenter.DescribeSCFileTaskReply.data:type_name -> jdstack.zeus.v1.jdstackFilecenter.DescribeSCFilesResponseData
	32, // 16: jdstack.zeus.v1.jdstackFilecenter.AppFilesTaskReq.artifactInfos:type_name -> jdstack.zeus.v1.jdstackFilecenter.pushFileInfo
	38, // 17: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeDeploymentServerInfo:input_type -> google.protobuf.Empty
	2,  // 18: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateFileDownloadTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.CreateFileDownloadReq
	16, // 19: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeFileDownloadTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	31, // 20: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateAppFilesPushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.AppFilesTaskReq
	16, // 21: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeAppFilesPushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	5,  // 22: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.ModifyArtifactAndMetadataBySeal:input_type -> jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataBySealReq
	4,  // 23: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.ModifyArtifactAndMetadataByDeploy:input_type -> jdstack.zeus.v1.jdstackFilecenter.ModifyArtifactAndMetadataByDeployReq
	19, // 24: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribesArtifact:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReq
	22, // 25: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateArtifactPackageTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.CreateArtifactPackageReq
	16, // 26: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeArtifactPackageTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	30, // 27: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateSCFilesPushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.CreateSCFilesTaskReq
	16, // 28: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeSCFilesPushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	1,  // 29: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateUploadTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.CreateUploadTaskReq
	34, // 30: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateArtifact:input_type -> jdstack.zeus.v1.jdstackFilecenter.CreateArtifactReq
	9,  // 31: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateRegularFilePushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.RegularFileTaskReq
	10, // 32: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateRpmPushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.RpmTaskReq
	11, // 33: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateSkywingPackagePushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.SkywingPackageTaskReq
	12, // 34: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateDockerImagePushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DockerImageTaskReq
	16, // 35: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeRegularFilePushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	16, // 36: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeRpmPushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	16, // 37: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeSkywingPackagePushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	16, // 38: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeDockerImagePushTask:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	25, // 39: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateMetadataPackage:input_type -> jdstack.zeus.v1.jdstackFilecenter.CreateMetadataPackageReq
	16, // 40: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeMetadataPackage:input_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReq
	35, // 41: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeDeploymentServerInfo:output_type -> jdstack.zeus.v1.jdstackFilecenter.DeploymentServerInfoReply
	14, // 42: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateFileDownloadTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReply
	3,  // 43: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeFileDownloadTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeFileDownloadReply
	14, // 44: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateAppFilesPushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReply
	33, // 45: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeAppFilesPushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeAppFileTaskReply
	6,  // 46: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.ModifyArtifactAndMetadataBySeal:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeArtifactAndMetadataBySealReply
	6,  // 47: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.ModifyArtifactAndMetadataByDeploy:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeArtifactAndMetadataBySealReply
	20, // 48: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribesArtifact:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribesArtifactReply
	14, // 49: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateArtifactPackageTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReply
	24, // 50: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeArtifactPackageTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeArtifactPackageReply
	14, // 51: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateSCFilesPushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeTaskReply
	29, // 52: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeSCFilesPushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeSCFilesResponseData
	39, // 53: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateUploadTask:output_type -> jdstack.zeus.v1.goodsStack.DescribesFilecenterReply
	13, // 54: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateArtifact:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonReply
	13, // 55: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateRegularFilePushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonReply
	13, // 56: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateRpmPushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonReply
	13, // 57: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateSkywingPackagePushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonReply
	13, // 58: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateDockerImagePushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonReply
	18, // 59: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeRegularFilePushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonFileTaskReply
	18, // 60: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeRpmPushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonFileTaskReply
	18, // 61: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeSkywingPackagePushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonFileTaskReply
	18, // 62: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeDockerImagePushTask:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonFileTaskReply
	13, // 63: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.CreateMetadataPackage:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeCommonReply
	24, // 64: jdstack.zeus.v1.jdstackFilecenter.JdstackFilecenter.DescribeMetadataPackage:output_type -> jdstack.zeus.v1.jdstackFilecenter.DescribeArtifactPackageReply
	41, // [41:65] is the sub-list for method output_type
	17, // [17:41] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_jdstack_filecenter_proto_init() }
func file_jdstack_filecenter_proto_init() {
	if File_jdstack_filecenter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_jdstack_filecenter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilePropertiesRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUploadTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateFileDownloadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeFileDownloadReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyArtifactAndMetadataByDeployReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyArtifactAndMetadataBySealReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeArtifactAndMetadataBySealReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArtifactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegularFileTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RpmTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkywingPackageTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DockerImageTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeCommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushTaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeCommonFileTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribesArtifactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribesArtifactReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppArtifact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateArtifactPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeArtifactPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeArtifactPackageReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMetadataPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PackageTaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeMetadataPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSCFileTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSCFilesResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSCFilesTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppFilesTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushFileInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppFileTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateArtifactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jdstack_filecenter_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentServerInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_jdstack_filecenter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_jdstack_filecenter_proto_goTypes,
		DependencyIndexes: file_jdstack_filecenter_proto_depIdxs,
		MessageInfos:      file_jdstack_filecenter_proto_msgTypes,
	}.Build()
	File_jdstack_filecenter_proto = out.File
	file_jdstack_filecenter_proto_rawDesc = nil
	file_jdstack_filecenter_proto_goTypes = nil
	file_jdstack_filecenter_proto_depIdxs = nil
}
