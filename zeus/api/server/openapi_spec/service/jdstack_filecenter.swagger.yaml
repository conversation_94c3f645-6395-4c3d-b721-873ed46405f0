swagger: "2.0"
info:
  title: JDStackFilecenter_JDStack文件管理
  version: v1
paths:
  /filecenter:DeploymentServerInfo:
    get:
      description: 获取部署服务器信息
      operationId: describeJdstackFilecenterDeploymentServerInfo
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDeploymentServerInfoReply
        default:
          description: An unexpected error response.
  /filecenter:download:
    post:
      description: 下载文件到公共目录（异步接口-创建）
      operationId: createJdstackFilecenterFileDownloadTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterCreateFileDownloadReq
  /filecenter:pushAllFile:
    post:
      description: 推送sc所有制品到环境仓库（异步接口-创建）
      operationId: createJdstackFilecenterSCFilesPushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterCreateSCFilesTaskReq
  /filecenter:pushAppFile:
    post:
      description: 推送产品所有制品到环境仓库（异步接口-创建）
      operationId: createJdstackFilecenterAppFilesPushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterAppFilesTaskReq
  /filecenter:pushDockerImage:
    post:
      description: 推送docker镜像到环境仓库（异步接口-创建）
      operationId: createJdstackFilecenterDockerImagePushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDockerImageTaskReq
  /filecenter:pushRegularFile:
    post:
      description: 推送普通文件制品到环境仓库（异步接口-创建）
      operationId: createJdstackFilecenterRegularFilePushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterRegularFileTaskReq
  /filecenter:pushRpm:
    post:
      description: 推送rpm制品到环境仓库（异步接口-创建）
      operationId: createJdstackFilecenterRpmPushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterRpmTaskReq
  /filecenter:pushSkywingPackage:
    post:
      description: 推送云翼包制品到环境仓库（异步接口-创建）
      operationId: createJdstackFilecenterSkywingPackagePushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonReply
        default:
          description: An unexpected error response.
      parameters:
        - x-jdcloud-tiered: false
          name: body
          in: body
          required: true
          schema:
            $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterSkywingPackageTaskReq
  /filecenter:queryArtifactPackage:
    get:
      description: 导出制品离线包（异步接口-查询）
      operationId: describeJdstackFilecenterArtifactPackageTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeArtifactPackageReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryDownload:
    get:
      description: 下载文件到公共目录（异步接口-查询）
      operationId: describeJdstackFilecenterFileDownloadTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeFileDownloadReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryMetadataPackage:
    get:
      description: 导出封板元数据（异步接口-查询）
      operationId: describeJdstackFilecenterMetadataPackage
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeArtifactPackageReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryPushAllFile:
    get:
      description: 查询推送sc所有制品到环境仓库（异步接口-查询）
      operationId: describeJdstackFilecenterSCFilesPushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeSCFilesResponseData
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryPushAppFile:
    get:
      description: 查询推送应用所有制品到环境仓库（异步接口-查询）
      operationId: describeJdstackFilecenterAppFilesPushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeAppFileTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryPushDockerImage:
    get:
      description: 查询推送docker镜像到环境仓库（异步接口-查询）
      operationId: describeJdstackFilecenterDockerImagePushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonFileTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryPushRegularFile:
    get:
      description: 查询推送普通文件制品到环境仓库（异步接口-查询）
      operationId: describeJdstackFilecenterRegularFilePushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonFileTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryPushRpm:
    get:
      description: 查询推送rpm制品到环境仓库（异步接口-查询）
      operationId: describeJdstackFilecenterRpmPushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonFileTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /filecenter:queryPushSkywingPackage:
    get:
      description: 查询推送云翼包制品到环境仓库（异步接口-查询）
      operationId: describeJdstackFilecenterSkywingPackagePushTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonFileTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - name: taskID
          in: query
          required: false
          type: string
  /version/{packageVersion}/filecenter:exportArtifactPackage:
    post:
      description: 导出制品离线包（异步接口-创建）
      operationId: createJdstackFilecenterArtifactPackageTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeTaskReply
        default:
          description: An unexpected error response.
      parameters:
        - name: packageVersion
          description: 部署包版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              architecture:
                type: string
                enum:
                  - amd64
                  - arm64
                description: "部署包使用CPU架构 : \n[amd64, arm64]"
              baseVersion:
                type: string
                description: 用于生成增量部署包的基准版本
              clearArtifactDirBeforePackage:
                type: boolean
                description: 是否在触发打包前清空制品目录,目前仅清空当前架构目录和公共yum目录
              destHostIp:
                type: string
                description: 目标服务器IP地址
              destPath:
                type: string
                description: 部署包存放路径
              destSshPort:
                type: integer
                format: int64
                description: 打包服务器ssh端口
              hostOsTypes:
                type: array
                items:
                  type: string
                description: "操作系统类型(支持多选): \n[centos, openeuler22, kylinv10]"
              jdstackReleaseS3:
                type: string
                description: 版本ADL包存储位置S3,如果不为空,则运行相应命令下载到指定位置
              packageVersionType:
                type: string
                description: 部署包版本类型
              snapshotGeneratedTimeStamp:
                type: integer
                format: int64
                description: 快照生成时间戳,秒级别
              specResources:
                type: array
                items:
                  type: string
                description: 指定产品(resource)出包预留字段
              taskType:
                type: string
                enum:
                  - increment
                  - full
                description: "部署包任务类型: \n[increment, full]"
            required:
              - destHostIp
              - destPath
              - taskType
              - architecture
              - hostOsTypes
  /version/{packageVersion}/filecenter:exportMetadataPackage:
    post:
      description: 导出封板元数据（异步接口-创建）
      operationId: createJdstackFilecenterMetadataPackage
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonReply
        default:
          description: An unexpected error response.
      parameters:
        - name: packageVersion
          description: 部署包版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              baseVersion:
                type: string
                description: 用于生成增量部署包的基准版本
              destHostIp:
                type: string
                description: 目标服务器IP地址
              destPath:
                type: string
                description: 部署包存放路径
              destSshPort:
                type: integer
                format: int64
                description: 打包服务器ssh端口
              taskType:
                type: string
                enum:
                  - increment
                  - full
                description: 部署包任务类型
            required:
              - destHostIp
              - destPath
              - taskType
              - architecture
              - hostOsType
  /version/{version}/filecenter:artifact:
    post:
      description: 创建制品信息
      operationId: createJdstackFilecenterArtifact
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeCommonReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              appCode:
                type: string
                description: 应用Code
              arch:
                type: string
                description: |-
                  架构:
                  [ x86_64 arm ]
              directorySpec:
                type: string
                description: 目录命名规范：DirectorySpecArchFileTypeServiceCode、DirectorySpecArchSerializeGoods
              fileHash:
                type: string
                description: 文件校验码
              fileIdentifier:
                type: string
                description: 文件或镜像标识
              fileSize:
                type: integer
                format: int64
                description: 文件大小/字节
              fileVersion:
                type: string
                description: 文件版本
              filename:
                type: string
                description: 文件或镜像名称
              hostOsType:
                type: string
                description: 操作系统发行版：HostOsTypeCentos、HostOsTypeOpenEuler
              imageTags:
                type: string
                description: 文件或镜像版本
              param:
                type: string
                description: 参数
  /version/{version}/filecenter:artifacts:
    post:
      description: 根据版本和应用返回制品列表
      operationId: describeJdstackFilecentersArtifact
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribesArtifactReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              appName:
                type: string
                description: 应用名称
              architecture:
                type: string
                description: |-
                  架构:
                  [x86_64 arm]
              hostOSType:
                type: string
                description: |-
                  操作系统:
                  [ centos openEuler kylin]
              serviceCode:
                type: string
                description: Service code
            required:
              - architecture
  /version/{version}/filecenter:updateByDeploy:
    post:
      description: 根据部署更新制品和应用元数据
      operationId: modifyJdstackFilecenterArtifactAndMetadataByDeploy
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeArtifactAndMetadataBySealReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              appInfo:
                $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterApplicationInfoReq
                description: 应用信息
              category:
                type: string
                description: 分类名称
              cloudType:
                type: string
                description: 平台类型
              policy:
                type: array
                items:
                  type: string
                description: "策略:\noverride - 覆盖相同serviceCode、appName、arch、os、identifier文件信息 \nautoOfflineOthers - 自动下线其他关联"
              productCode:
                type: string
                description: 产品Code
              productName:
                type: string
                description: 产品名称
              serviceCode:
                type: string
                description: serviceCode
  /version/{version}/filecenter:updateBySeal:
    post:
      description: 根据封板更新制品和产品应用元数据
      operationId: modifyJdstackFilecenterArtifactAndMetadataBySeal
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribeArtifactAndMetadataBySealReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              adlFiles:
                type: array
                items:
                  $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterAdlReq
                description: 产品级别的adl文件
              appInfos:
                type: array
                items:
                  $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterApplicationInfoReq
                description: 应用信息
              businessMetadata:
                $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterUnifiedMetadataCreateReq
                description: 产品关联的的商业平台元数据信息
              category:
                type: string
                description: 分类名称
              cloudType:
                type: string
                description: 平台类型
              policy:
                type: array
                items:
                  type: string
                description: |-
                  策略:
                  override - 覆盖相同serviceCode、appName、arch、os、identifier文件信息
              productCode:
                type: string
                description: 产品Code
              productName:
                type: string
                description: 产品名称
              productYaml:
                type: string
                description: pd描述
              serviceCode:
                type: string
                description: serviceCode
  /version/{version}/filecenter:uploadTask:
    post:
      description: 上传文件或镜像
      operationId: createJdstackFilecenterUploadTask
      responses:
        "200":
          x-jdcloud-tiered: false
          description: A successful response.
          schema:
            properties:
              error:
                properties:
                  errorObject:
                    $ref: ../../common/model/Err.yaml#/definitions/err
              requestId:
                type: string
              result:
                properties:
                  resultObject:
                    $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterDescribesFilecenterReply
        default:
          description: An unexpected error response.
      parameters:
        - name: version
          description: JDStack版本
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              appName:
                type: string
                description: 应用名称
              arch:
                type: string
                description: |-
                  架构:
                  [ x86_64 arm ]
              envName:
                type: string
                description: 上传环境名称,需Filecenter脚本支持,默认空=仅改Filecenter不往环境推送
              files:
                type: array
                items:
                  $ref: ../module/jdstack_filecenter.swagger.yaml#/definitions/JdstackFilecenterFilePropertiesRes
                description: 文件信息
              isStore:
                type: boolean
                description: 是否要将文件或镜像信息落库
              os:
                type: string
                description: |-
                  操作系统:
                  [ centos openEuler kylin]
              policy:
                type: array
                items:
                  type: string
                description: |-
                  策略:
                  override - 覆盖相同serviceCode、appName、arch、os、identifier文件信息
              serviceCode:
                type: string
                description: 产品名称
            required:
              - serviceCode
              - arch
              - os
              - files
