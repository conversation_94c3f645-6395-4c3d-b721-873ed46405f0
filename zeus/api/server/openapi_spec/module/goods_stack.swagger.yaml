swagger: "2.0"
info:
definitions:
  GoodsStackAdlReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      content:
        type: string
        description: adl文件内容
      filePath:
        type: string
        description: |-
          adl文件相对路径
          - 相对于adl项目的文件路径,包含文件名
      scope:
        type: string
        enum:
          - app
          - product
          - serviceCode
        description: "adl级别 : \n[app, product, serviceCode]"
    required:
      - filePath
      - content
  GoodsStackCreateAdlReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      adl:
        $ref: '#/definitions/GoodsStackAdlReq'
        description: 商业平台元数据内容
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品adl
    required:
      - content
  GoodsStackCreateApplicationReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        default: jdstack
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      deployMode:
        type: string
        description: |-
          部署模式:
          [ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]
      highAvailabilityDesignPatterns:
        type: string
        description: |-
          高可用设计模式:
          [ stateless cluster cold_backup mongodb flink ]
      name:
        type: string
        description: 应用名称
      nameCn:
        type: string
        description: 应用中文名称
      xingyunName:
        type: string
        description: 行云名称
    required:
      - name
      - deployMode
  GoodsStackCreateCategoryReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        default: jdstack
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      nameCn:
        type: string
        description: 分类中文名称
    required:
      - nameCn
  GoodsStackCreateChainReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      fromGoods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 对端标品信息
      policy:
        type: array
        items:
          type: string
        description: |-
          策略
          override - 覆盖相同本端标品信息,对端标品信息,关联类型,关联ID
      resId:
        type: integer
        format: int64
        description: 关联ID
      resKind:
        type: string
        description: |-
          关联类型:
          [ domain middleware delivery upgrade ]
      toGoods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 本端标品信息
    required:
      - toGoodsId
      - fromGoodsId
      - resKind
  GoodsStackCreateConfigReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      data:
        type: string
        description: 配置数据
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      kind:
        type: string
        description: |-
          配置类型:
          [ deployType ]
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品
    required:
      - content
  GoodsStackCreateDeliveryReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      must:
        type: string
        description: |-
          是否必部署:
          [ no yes ]
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品
    required:
      - must
  GoodsStackCreateDeliveryRuleGroupReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      name:
        type: string
        description: 标品交付规则组名称
  GoodsStackCreateDomainReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      confBlock:
        type: array
        items:
          type: string
        description: 配置块
      description:
        type: string
        description: 描述
      domainUse:
        type: string
        description: |-
          域名类型:
          [ loadBalancer a ]
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品,域名模板,域名端口
      port:
        type: integer
        format: int32
        default: "0"
        description: 域名端口号
      protocol:
        type: string
        description: |-
          协议:
          [ http https tcp ]
      targetPort:
        type: integer
        format: int32
        default: "0"
        description: 服务端口号
      template:
        type: string
        description: 域名模板
    required:
      - template
      - domainUse
  GoodsStackCreateFilecenterReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      arch:
        type: string
        description: 架构类型
      directorySpec:
        type: string
        description: 目录命名规范
      fileHash:
        type: string
        description: 文件校验码
      fileIdentifier:
        type: string
        description: 文件或镜像标识
      fileName:
        type: string
        description: 文件名称
      filePath:
        type: string
        description: 文件路径
      fileSize:
        type: integer
        format: int64
        description: 文件大小/字节
      fileType:
        type: string
        description: 文件类型
      fileVersion:
        type: string
        description: 文件版本
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      hostOsType:
        type: string
        description: 操作系统发行版
      imageTags:
        type: string
        description: 文件或镜像版本
      param:
        type: string
        description: 参数
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品,架构,文件或镜像标识,操作系统发行版
      remark:
        type: string
        description: 备注
    required:
      - arch
      - deployMode
      - hostOsType
      - fileName
      - hostOsType
  GoodsStackCreateFlavorReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      arch:
        type: string
        description: |-
          架构类型:
          [ x86_64 arm ]
      clusterSize:
        type: string
        description: |-
          集群规模:
          [ poc light small middle large ]
      coreLimit:
        type: integer
        format: int32
        description: CPU极限/‰核
      coreRequest:
        type: integer
        format: int32
        description: CPU申请/‰核
      diskLimit:
        type: integer
        format: int32
        description: 硬盘极限/Gi
      diskRequest:
        type: integer
        format: int32
        description: 硬盘申请/Gi
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      memoryLimit:
        type: integer
        format: int32
        description: 内存极限/Mi
      memoryRequest:
        type: integer
        format: int32
        description: 内存申请/Mi
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品,集群规模,架构类型
      replicas:
        type: integer
        format: int32
        description: 副本数
    required:
      - clusterSize
      - arch
  GoodsStackCreateHardwareRoleUnionReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息,标品已存在时仍然可以继续新增标品关联
      hardwareRole:
        type: array
        items:
          $ref: '#/definitions/GoodsStackHardwareRoleDescribeReq'
        description: 关联硬件角色信息
      hardwareRoleLabel:
        type: array
        items:
          $ref: '#/definitions/GoodsStackCreateLabelReq'
        description: 关联硬件角色标签信息
      policy:
        type: array
        items:
          type: string
        description: '策略:'
    required:
      - goods
  GoodsStackCreateIacReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      mode:
        type: string
        description: |-
          交付类型:
          [ deploy upgrade ]
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品,交付类型
    required:
      - mode
  GoodsStackCreateLabelReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      description:
        type: string
        description: 描述
      hardwareRole:
        $ref: '#/definitions/GoodsStackHardwareRoleDescribeReq'
        description: 关联硬件角色信息
      id:
        type: integer
        format: int64
        description: ID
      key:
        type: string
        description: 标签键
      value:
        type: string
        description: 标签值
    required:
      - key
      - value
  GoodsStackCreateMetadataReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品pd
      unifiedMetadata:
        $ref: '#/definitions/GoodsStackUnifiedMetadataCreateReq'
        description: 商业平台元数据内容
    required:
      - content
  GoodsStackCreateMiddlewareReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      coreLimit:
        type: integer
        format: int32
        default: "0"
        description: CPU极限/‰核
      coreRequest:
        type: integer
        format: int32
        default: "0"
        description: CPU申请/‰核
      diskLimit:
        type: integer
        format: int32
        default: "0"
        description: 硬盘极限/Gi
      diskRequest:
        type: integer
        format: int32
        default: "0"
        description: 硬盘申请/Gi
      domainUse:
        type: string
        description: |-
          域名类型:
          [ loadBalancer a ]
      goods:
        type: array
        items:
          $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息,中间件已存在时仍然可以继续新增标品关联,policy[FDK:Goods]
      kind:
        type: string
        description: |-
          软件类型:
          [ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]
      memoryLimit:
        type: integer
        format: int32
        default: "0"
        description: 内存极限/Mi
      memoryRequest:
        type: integer
        format: int32
        default: "0"
        description: 内存申请/Mi
      name:
        type: string
        description: 中间件唯一标识
      params:
        $ref: '#/definitions/GoodsStackMiddlewareParams'
        description: 配置
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品,中间件唯一标识,软件类型
          foreignKeyDiscarded - 查出已存在的关联数据,本次请求没关联上就被淘汰,需指定FKD:{模块名}
      port:
        type: integer
        format: int32
        default: "0"
        description: 域名端口号
      protocol:
        type: string
        description: |-
          协议:
          [ http https tcp ]
      shareMode:
        type: string
        default: "no"
        description: |-
          共享类型:
          [ no yes ]
      targetPort:
        type: integer
        format: int32
        description: 服务端口号
      urlTemplate:
        type: string
        description: URL
    required:
      - name
      - kind
      - domainUse
  GoodsStackCreatePdReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      content:
        type: string
        description: 产品pd的内容
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReq'
        description: 关联标品信息
      policy:
        type: array
        items:
          type: string
        description: |-
          策略:
          override - 覆盖相同标品pd
    required:
      - content
  GoodsStackCreateResourceReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        default: jdstack
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      deploymentRole:
        type: string
        description: 部署角色
      name:
        type: string
        description: 服务名称
      nameCn:
        type: string
        description: 服务中文名称
      scope:
        type: string
        description: |-
          产品范围:
          [ global region availabilityZone ]
      xingyunName:
        type: string
        description: 行云名称
    required:
      - name
      - scope
  GoodsStackCreateServiceReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        default: jdstack
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      deploymentPatterns:
        type: string
        description: |-
          部署形态:
          [ master_slave cluster cold_backup multi_active ]
      name:
        type: string
        description: 产品名称
      nameCn:
        type: string
        description: 产品中文名称
    required:
      - name
  GoodsStackDeliveryReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      goodsId:
        type: string
        description: 关联标品ID,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      must:
        type: string
        description: |-
          是否必部署:
          [ no yes ]
  GoodsStackDeliveryRuleGroupReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 标品交付规则组ID
      name:
        type: string
        description: 标品交付规则组名称
  GoodsStackDescribeApplicationReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      deployMode:
        type: string
        description: |-
          部署模式:
          [ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]
      highAvailabilityDesignPatterns:
        type: string
        description: |-
          高可用设计模式:
          [ stateless cluster cold_backup mongodb flink ]
      id:
        type: integer
        format: int64
        description: 应用ID
      name:
        type: string
        description: 应用名称
      nameCn:
        type: string
        description: 应用中文名称
      xingyunName:
        type: string
        description: 行云名称
  GoodsStackDescribeCategoryReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      id:
        type: integer
        format: int64
        description: 分类ID
      nameCn:
        type: string
        description: 分类中文名称
  GoodsStackDescribeChainReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      clusterType:
        type: string
        description: |-
          部署集群类型,当前仅限云舰版本使用:
          [ master slave ]
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      deployType:
        type: string
        description: |-
          交付类型,当前仅云舰版本使用,融合场景或标准交付场景:
          [ fusion standard ]
      domain:
        $ref: '#/definitions/GoodsStackDomainReply'
        description: 关联类型Domain数据
      fromGoods:
        $ref: '#/definitions/GoodsStackLess'
        description: 关联对端标品信息
      id:
        type: integer
        format: int64
        description: ID
      middleware:
        $ref: '#/definitions/GoodsStackMiddlewareReply'
        description: 关联类型Middleware数据
      resId:
        type: integer
        format: int64
        description: 关联ID
      resKind:
        type: string
        description: |-
          关联类型:
          [ domain middleware delivery upgrade ]
      toGoods:
        $ref: '#/definitions/GoodsStackLess'
        description: 关联本端标品信息
  GoodsStackDescribeConfigReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      data:
        type: string
        description: 配置数据
      id:
        type: integer
        format: int64
        description: ID
      kind:
        type: string
        description: |-
          配置类型:
          [ deployType ]
      updatedAt:
        type: string
        description: 更新时间,UTC格式(毫秒级24位)
  GoodsStackDescribeDeliveryReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReply'
        description: 关联标品,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      must:
        type: string
        description: |-
          是否必部署:
          [ no yes ]
  GoodsStackDescribeDeliveryRuleGroupReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      DeliveryRule:
        type: array
        items:
          $ref: '#/definitions/GoodsStackGoodsDeliveryRuleDescribeReply'
        description: 标品交付规则信息
      desc:
        type: string
        description: 标品交付规则组描述
      id:
        type: integer
        format: int64
        description: 标品交付规则组ID
      name:
        type: string
        description: 标品交付规则组名称
  GoodsStackDescribeDomainReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      confBlock:
        type: array
        items:
          type: string
        description: 配置块
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      domainUse:
        type: string
        description: |-
          域名类型:
          [ loadBalancer a ]
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReply'
        description: 关联标品,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      port:
        type: integer
        format: int32
        description: 域名端口号
      protocol:
        type: string
        description: |-
          协议:
          [ http https tcp ]
      targetPort:
        type: integer
        format: int32
        description: 服务端口号
      template:
        type: string
        description: 域名模板
  GoodsStackDescribeFilecenterReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      arch:
        type: string
        description: |-
          架构:
          [ x86 arm ]
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      deployMode:
        type: string
        description: |-
          部署模式:
          [ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_image xingyun_package ]
      fileHash:
        type: string
        description: 文件校验码
      fileIdentifier:
        type: string
        description: 文件或镜像标识
      fileName:
        type: string
        description: 文件名称
      fileSize:
        type: integer
        format: int64
        description: 文件大小/字节
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReply'
        description: 关联标品,filter[With:Goods]
      helm:
        $ref: '#/definitions/GoodsStackFilecenterDeployDockerImageReply'
        description: 按部署模式 [ helm ] 分组
      hostOsType:
        type: string
        description: |-
          操作系统发行版:
          [ centos openEuler kylin]
      iaasImage:
        $ref: '#/definitions/GoodsStackFilecenterDeployIaasImageReply'
        description: 按部署模式 [ iaas_image ] 分组
      id:
        type: integer
        format: int64
        description: ID
      mateBin:
        $ref: '#/definitions/GoodsStackFilecenterDeployMateBinReply'
        description: 按部署模式 [ mate_bin ] 分组
      mateContainer:
        $ref: '#/definitions/GoodsStackFilecenterDeployDockerImageReply'
        description: 按部署模式 [ mate_container ] 分组
      mateV2:
        $ref: '#/definitions/GoodsStackFilecenterDeployRpmReply'
        description: 按部署模式 [ mate_v2 ] 分组
      rpm:
        $ref: '#/definitions/GoodsStackFilecenterDeployRpmReply'
        description: 按部署模式 [ rpm ] 分组
      shell:
        $ref: '#/definitions/GoodsStackFilecenterDeployShellReply'
        description: 按部署模式 [ shell ] 分组
      skywingContainer:
        $ref: '#/definitions/GoodsStackFilecenterDeployDockerImageReply'
        description: 按部署模式 [ skywing_container ] 分组
      skywingPackage:
        $ref: '#/definitions/GoodsStackFilecenterDeploySkywingPackageReply'
        description: 按部署模式 [ skywing_package ] 分组
  GoodsStackDescribeFlavorReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      arch:
        type: string
        description: |-
          架构类型:
          [ x86_64 arm ]
      clusterSize:
        type: string
        description: |-
          集群规模:
          [ poc light small middle large ]
      coreLimit:
        type: integer
        format: int32
        description: CPU极限/‰核
      coreRequest:
        type: integer
        format: int32
        description: CPU申请/‰核
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      diskLimit:
        type: integer
        format: int32
        description: 硬盘极限/Gi
      diskRequest:
        type: integer
        format: int32
        description: 硬盘申请/Gi
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReply'
        description: 关联标品,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      memoryLimit:
        type: integer
        format: int32
        description: 内存极限/Mi
      memoryRequest:
        type: integer
        format: int32
        description: 内存申请/Mi
      replicas:
        type: integer
        format: int32
        description: 副本数
  GoodsStackDescribeHardwareRoleLabelReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      goods:
        type: array
        items:
          $ref: '#/definitions/GoodsStackGoodsStackReply'
        description: 关联标品,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      key:
        type: string
        description: 标签键
      role:
        $ref: '#/definitions/GoodsStackHardwareRoleReply'
        description: 关联硬件角色信息,filter[With:HardwareRole]
      value:
        type: string
        description: 标签值
  GoodsStackDescribeHardwareRoleReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      fromVersion:
        $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 始于JDStack版本信息,filter[With:FormJdstackRelease]
      goods:
        type: array
        items:
          $ref: '#/definitions/GoodsStackGoodsStackReply'
        description: 关联标品,filter[With:Goods]
      hardware:
        type: string
        description: |-
          硬件类型:
          [ server network ]
      id:
        type: integer
        format: int64
        description: ID
      label:
        type: array
        items:
          $ref: '#/definitions/GoodsStackHardwareRoleLabelReply'
        description: 关联硬件角色标签信息,filter[With:HardwareRoleLabel]
      name:
        type: string
        description: 角色名称
      serverPlane:
        type: array
        items:
          $ref: '#/definitions/GoodsStackServerPlaneDescribeReply'
        description: 关联服务器规划信息,filter[With:ServerPlane]
      version:
        type: array
        items:
          $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 关联JDStack版本信息,filter[With:JdstackRelease]
  GoodsStackDescribeIacReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      goods:
        $ref: '#/definitions/GoodsStackGoodsStackDescribeReply'
        description: 关联标品,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      mode:
        type: string
        description: |-
          交付类型:
          [ deploy upgrade ]
  GoodsStackDescribeLabelReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      hardwareRole:
        $ref: '#/definitions/GoodsStackHardwareRoleReply'
        description: 关联硬件角色信息,filter[With:HardwareRole]
      id:
        type: integer
        format: int64
        description: ID
      key:
        type: string
        description: 标签键
      value:
        type: string
        description: 标签值
  GoodsStackDescribeMetadataReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      id:
        type: integer
        format: int64
        description: ID
      type:
        type: string
        description: |-
          元数据类型:
          [ business apigw ]
      unifiedMetadataId:
        type: integer
        format: int64
        description: 存储元数据主键Id
      updatedAt:
        type: string
        description: 更新时间,UTC格式(毫秒级24位)
      versionId:
        type: string
        description: 元数据唯一标识Id
      versionName:
        type: string
        description: 元数据唯一标识
  GoodsStackDescribeMiddlewareReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      coreLimit:
        type: integer
        format: int32
        description: CPU极限/‰核
      coreRequest:
        type: integer
        format: int32
        description: CPU申请/‰核
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      diskLimit:
        type: integer
        format: int32
        description: 硬盘极限/Gi
      diskRequest:
        type: integer
        format: int32
        description: 硬盘申请/Gi
      domainUse:
        type: string
        description: |-
          域名类型:
          [ loadBalancer a ]
      goods:
        type: array
        items:
          $ref: '#/definitions/GoodsStackGoodsStackDescribeReply'
        description: 关联标品,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      kind:
        type: string
        description: |-
          软件类型:
          [ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]
      memoryLimit:
        type: integer
        format: int32
        description: 内存极限/Mi
      memoryRequest:
        type: integer
        format: int32
        description: 内存申请/Mi
      name:
        type: string
        description: 中间件唯一标识
      params:
        $ref: '#/definitions/GoodsStackMiddlewareParams'
        description: 配置
      port:
        type: integer
        format: int32
        description: 域名端口号
      protocol:
        type: string
        description: |-
          协议:
          [ http https tcp ]
      shareMode:
        type: string
        description: |-
          共享类型:
          [ no yes ]
      targetPort:
        type: integer
        format: int32
        description: 服务端口号
      urlTemplate:
        type: string
        description: URL
  GoodsStackDescribePdReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      content:
        type: string
        description: pd内容
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      id:
        type: integer
        format: int64
        description: ID
      updatedAt:
        type: string
        description: 更新时间,UTC格式(毫秒级24位)
  GoodsStackDescribeResourceReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      deploymentRole:
        type: string
        description: 部署角色,用于选择部署机器
      dummyType:
        type: string
        description: 虚拟产品类型,用于云舰版本元数据
      id:
        type: integer
        format: int64
        description: 服务ID
      name:
        type: string
        description: 服务名称
      nameCn:
        type: string
        description: 服务中文名称
      scope:
        type: string
        description: |-
          产品范围:
          [ global region availabilityZone ]
      xingyunName:
        type: string
        description: 行云名称
  GoodsStackDescribeServiceReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      deploymentPatterns:
        type: string
        description: |-
          部署形态:
          [ master_slave cluster cold_backup multi_active ]
      id:
        type: integer
        format: int64
        description: 产品ID
      name:
        type: string
        description: 产品名称
      nameCn:
        type: string
        description: 产品中文名称
  GoodsStackDescribeUpgradeReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      content:
        type: string
        description: 升级描述内容
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      id:
        type: integer
        format: int64
        description: ID
      updatedAt:
        type: string
        description: 更新时间,UTC格式(毫秒级24位)
  GoodsStackDescribesChainReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeChainReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesDeliveryReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeDeliveryReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesDomainReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeDomainReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesFilecenterReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeFilecenterReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesFlavorReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeFlavorReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesHardwareRoleLabelReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeHardwareRoleLabelReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesHardwareRoleReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeHardwareRoleReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesIacReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeIacReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDescribesMiddlewareReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeMiddlewareReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackDomainReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      confBlock:
        type: array
        items:
          type: string
        description: 配置块
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      domainUse:
        type: string
        description: |-
          域名类型:
          [ loadBalancer a ]
      goodsId:
        type: string
        description: 关联标品ID,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      port:
        type: integer
        format: int32
        description: 域名端口号
      protocol:
        type: string
        description: |-
          协议:
          [ http https tcp ]
      targetPort:
        type: integer
        format: int32
        description: 服务端口号
      template:
        type: string
        description: 域名模板
  GoodsStackFilecenterDeployDockerImageReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      registry:
        type: string
        description: 注册模块名
      tag:
        type: string
        description: 镜像版本
  GoodsStackFilecenterDeployIaasImageReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      attributes:
        type: string
        description: 镜像属性
      containerFormat:
        type: string
        description: |-
          镜像用途:
          [ docker vm ]
      diskFormat:
        type: string
        description: 镜像格式
      guestAgent:
        type: string
        description: 镜像agent类型
      imageId:
        type: string
        description: IaaS镜像ID
      imageName:
        type: string
        description: 镜像名称
      isProtected:
        type: string
        default: "no"
        description: |-
          是否为保护镜像:
          [ no yes ]
      osType:
        type: string
        description: 镜像操作系统类型
      platform:
        type: string
        description: |-
          镜像所属平台:
          [ windows linux ]
      rootDeviceType:
        type: string
        default: local
        description: |-
          镜像存储盘:
          [ local volume ]
      source:
        type: string
        description: |-
          镜像分类:
          [ jdcloud self ]
  GoodsStackFilecenterDeployMateBinReply:
    x-jdcloud-module: jdstackzeus
    type: object
  GoodsStackFilecenterDeployRpmReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      repo:
        type: string
        description: 文件存储位置
      tag:
        type: string
        description: 包版本
  GoodsStackFilecenterDeployShellReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      destPath:
        type: string
        description: 上传文件存放路径
      storageLocation:
        type: string
        description: |-
          文件存储位置:
          [ minio http ]
      tag:
        type: string
        description: 文件特征
  GoodsStackFilecenterDeploySkywingPackageReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      moduleName:
        type: string
        description: 云翼模块名称
      packageVersion:
        type: string
        description: 包版本
  GoodsStackFilecenterReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      arch:
        type: string
        description: |-
          架构:
          [ x86_64 arm ]
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      deployMode:
        type: string
        description: |-
          部署模式:
          [ skywing_container skywing_package mate_container shell helm rpm mate_bin mate_v2 iaas_image xingyun_container xingyun_package ]
      fileHash:
        type: string
        description: 文件校验码
      fileIdentifier:
        type: string
        description: 文件或镜像标识
      fileName:
        type: string
        description: 文件名称
      fileSize:
        type: integer
        format: int64
        description: 文件大小/字节
      goodsId:
        type: string
        description: 关联标品ID,filter[With:Goods]
      helm:
        $ref: '#/definitions/GoodsStackFilecenterDeployDockerImageReply'
        description: 按部署模式 [ helm ] 分组
      hostOsType:
        type: string
        description: |-
          操作系统发行版:
          [ centos openEuler kylin]
      iaasImage:
        $ref: '#/definitions/GoodsStackFilecenterDeployIaasImageReply'
        description: 按部署模式 [ iaas_image ] 分组
      id:
        type: integer
        format: int64
        description: ID
      mateBin:
        $ref: '#/definitions/GoodsStackFilecenterDeployMateBinReply'
        description: 按部署模式 [ mate_bin ] 分组
      mateContainer:
        $ref: '#/definitions/GoodsStackFilecenterDeployDockerImageReply'
        description: 按部署模式 [ mate_container ] 分组
      mateV2:
        $ref: '#/definitions/GoodsStackFilecenterDeployRpmReply'
        description: 按部署模式 [ mate_v2 ] 分组
      rpm:
        $ref: '#/definitions/GoodsStackFilecenterDeployRpmReply'
        description: 按部署模式 [ rpm ] 分组
      shell:
        $ref: '#/definitions/GoodsStackFilecenterDeployShellReply'
        description: 按部署模式 [ shell ] 分组
      skywingContainer:
        $ref: '#/definitions/GoodsStackFilecenterDeployDockerImageReply'
        description: 按部署模式 [ skywing_container ] 分组
      skywingPackage:
        $ref: '#/definitions/GoodsStackFilecenterDeploySkywingPackageReply'
        description: 按部署模式 [ skywing_package ] 分组
  GoodsStackFlavorReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      arch:
        type: string
        description: |-
          架构类型:
          [ x86_64 arm ]
      clusterSize:
        type: string
        description: |-
          集群规模:
          [ poc light small middle large ]
      coreLimit:
        type: integer
        format: int32
        description: CPU极限/‰核
      coreRequest:
        type: integer
        format: int32
        description: CPU申请/‰核
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      diskLimit:
        type: integer
        format: int32
        description: 硬盘极限/Gi
      diskRequest:
        type: integer
        format: int32
        description: 硬盘申请/Gi
      goodsId:
        type: string
        description: 关联标品ID,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      memoryLimit:
        type: integer
        format: int32
        description: 内存极限/Mi
      memoryRequest:
        type: integer
        format: int32
        description: 内存申请/Mi
      replicas:
        type: integer
        format: int32
        description: 副本数
  GoodsStackGoodsDeliveryRuleDescribeReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      delivery:
        type: string
        description: |-
          标品是否交付:
          [ no yes ]
          与规则[ defaultRegionDelivery deliveryArbitrationAz defaultAvailableZoneDelivery ] 对应
      deliveryRuleGroup:
        $ref: '#/definitions/GoodsStackDeliveryRuleGroupReply'
        description: 标品交付规则组信息
      description:
        type: string
        description: 标品交付规则描述
      goodsDeliveryRuleGroupID:
        type: integer
        format: int64
        description: 标品交付规则组ID
      id:
        type: integer
        format: int64
        description: 标品交付规则ID
      kind:
        type: string
        description: |-
          标品交付规则类型
          [ default custom ]
      maxNumber:
        type: integer
        format: int32
        description: |-
          标品交付规则值(与规则对应):
          [ regionAtEqual regionAtLeast regionAtMost regionAtAll availableZoneAtEqual availableZoneAtLeast availableZoneAtMost availableZoneAtAll ]
      minNumber:
        type: integer
        format: int32
        description: |-
          标品交付规则值(与规则对应):
          [ regionAtEqual regionAtLeast regionAtMost regionAtAll availableZoneAtEqual availableZoneAtLeast availableZoneAtMost availableZoneAtAll ]
      policy:
        type: string
        description: |-
          标品交付规则策略:
          [ required preferred ]
      reloadScope:
        type: string
        description: |-
          标品交付规则Reload范围:
          [ no global region ]
          与规则[ deliveryReload ]对应
      rule:
        type: string
        description: |-
          标品交付规则:
          [ regionAtEqual regionAtLeast regionAtMost regionAtAll availableZoneAtEqual availableZoneAtLeast availableZoneAtMost availableZoneAtAll deliveryArbitrationAz defaultRegionDelivery defaultAvailableZoneDelivery deliveryReload ]
      weight:
        type: integer
        format: int32
        description: |-
          标品交付规则权重(值越大,越优先)
          默认规则权重默认为1
          产品级别规则权重默认为2
          服务级别规则权重默认为3
          应用级别规则权重默认为4
  GoodsStackGoodsInfo:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      application:
        type: string
        description: 应用code
      category:
        type: string
        description: 分类
      resource:
        type: string
        description: 产品code
      service:
        type: string
        description: serviceCode
  GoodsStackGoodsStackDescribeReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      action:
        type: string
        description: |-
          操作行为:
          [ debug online offline upgrade ]
      application:
        $ref: '#/definitions/GoodsStackDescribeApplicationReply'
        description: 应用,filter[With:MetadataApplication]
      category:
        $ref: '#/definitions/GoodsStackDescribeCategoryReply'
        description: 分类,filter[With:MetadataCategory]
      chain:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeChainReply'
        description: 关联依赖信息,filter[With:GoodsChain]
      chainFrom:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeChainReply'
        description: 关联被依赖信息,filter[With:GoodsChain]
      cloudType:
        type: string
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      config:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeConfigReply'
        description: 关联配置,filter[With:GoodsConfig]
      delivery:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDeliveryReply'
        description: 关联交付信息,filter[With:GoodsDelivery]
      domain:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDomainReply'
        description: 关联域名信息,filter[With:GoodsDomain]
      filecenter:
        type: array
        items:
          $ref: '#/definitions/GoodsStackFilecenterReply'
        description: 关联文件或镜像信息,filter[With:GoodsFilecenter]
      flavor:
        type: array
        items:
          $ref: '#/definitions/GoodsStackFlavorReply'
        description: 关联云翼配置信息,filter[With:GoodsFlavor]
      goodsDeliveryRuleGroup:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeDeliveryRuleGroupReply'
        description: 关联被,filter[With:GoodsDeliveryRuleGroup.With:GoodsDeliveryRule]
      hardwareRole:
        type: array
        items:
          $ref: '#/definitions/GoodsStackHardwareRoleDescribeReply'
        description: 关联硬件角色信息,filter[With:HardwareRole]
      hardwareRoleLabel:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeLabelReply'
        description: 关联硬件角色标签信息,filter[With:HardwareRoleLabel]
      iac:
        type: array
        items:
          $ref: '#/definitions/GoodsStackIacReply'
        description: 关联IAC信息,filter[With:GoodsIac]
      id:
        type: string
        description: 标品ID
      metadata:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeMetadataReply'
        description: 产品级别元数据内容(商业平台/网关元数据),filter[With:GoodsMetadata]
      middleware:
        type: array
        items:
          $ref: '#/definitions/GoodsStackMiddlewareReply'
        description: 关联中间件信息,filter[With:GoodsMiddleware]
      pd:
        $ref: '#/definitions/GoodsStackDescribePdReply'
        description: 产品级别的product.yaml内容,filter[With:GoodsPd]
      resource:
        $ref: '#/definitions/GoodsStackDescribeResourceReply'
        description: 服务,filter[With:MetadataResource]
      service:
        $ref: '#/definitions/GoodsStackDescribeServiceReply'
        description: 产品,filter[With:MetadataService]
      upgrade:
        $ref: '#/definitions/GoodsStackDescribeUpgradeReply'
        description: 产品级别的升级描述内容,filter[With:GoodsUpgrade]
      version:
        $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 版本,filter[With:JdstackRelease]
      virtualGoods:
        type: string
        description: |-
          逻辑属性:
          [ no yes ]
  GoodsStackGoodsStackDescribeReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      application:
        type: string
        description: |-
          应用名称
          - 为空时按顺序查询字段：resource、service、category
      category:
        type: string
        description: 分类名称
      cloudType:
        type: string
        default: jdstack
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      filters:
        type: array
        items:
          $ref: ../../common/model/Filter.yaml#/definitions/filter
        description: |-
          返回字段皆支持筛选，筛选扩展:
          With:{模块名} - 返回关联表(此类过滤项无value，所以可将同类name合并name=.的values中)
          With:{模块名}.{字段名} - 过滤并返回关联表
      id:
        type: string
        description: |-
          标品ID
          - 为空时按顺序查询字段：application、resource、service、category
      resource:
        type: string
        description: |-
          服务名称
          - 为空时按顺序查询字段：service、category
      service:
        type: string
        description: |-
          产品名称
          - 为空时按顺序查询字段：category
      version:
        type: string
        description: JDStack版本
    required:
      - version
  GoodsStackGoodsStackDescribesReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/GoodsStackGoodsStackDescribeReply'
      totalCount:
        type: integer
        format: int64
  GoodsStackGoodsStackReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      action:
        type: string
        description: |-
          操作行为:
          [ debug online offline upgrade ]
      application:
        $ref: '#/definitions/GoodsStackDescribeApplicationReply'
        description: 应用,filter[With:MetadataApplication]
      category:
        $ref: '#/definitions/GoodsStackDescribeCategoryReply'
        description: 分类,filter[With:MetadataCategory]
      chain:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeChainReply'
        description: 关联依赖信息,filter[With:GoodsChain]
      chainFrom:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeChainReply'
        description: 关联被依赖信息,filter[With:GoodsChain]
      cloudType:
        type: string
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      hardwareRole:
        type: array
        items:
          $ref: '#/definitions/GoodsStackHardwareRoleDescribeReply'
        description: 关联物理机标签信息,filter[With:HardwareRole]
      hardwareRoleLabel:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeLabelReply'
        description: 关联物理机标签-高级属性信息,filter[With:HardwareRoleLabel]
      id:
        type: string
        description: 标品ID
      resource:
        $ref: '#/definitions/GoodsStackDescribeResourceReply'
        description: 服务,filter[With:MetadataResource]
      service:
        $ref: '#/definitions/GoodsStackDescribeServiceReply'
        description: 产品,filter[With:MetadataService]
      version:
        $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 版本,filter[With:JdstackRelease]
      virtualGoods:
        type: string
        description: |-
          逻辑属性:
          [ no yes ]
  GoodsStackHardwareRoleDescribeReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      fromVersion:
        $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 始于JDStack版本信息,filter[With:FormJdstackRelease]
      hardware:
        type: string
        description: |-
          硬件类型:
          [ server network ]
      id:
        type: integer
        format: int64
        description: ID
      label:
        type: array
        items:
          $ref: '#/definitions/GoodsStackDescribeLabelReply'
        description: 关联硬件角色标签信息,filter[With:HardwareRoleLabel]
      name:
        type: string
        description: 角色名称
      serverPlane:
        type: array
        items:
          $ref: '#/definitions/GoodsStackServerPlaneDescribeReply'
        description: 关联服务器规划信息,filter[With:ServerPlane]
      version:
        type: array
        items:
          $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 关联JDStack版本信息,filter[With:JdstackRelease]
  GoodsStackHardwareRoleDescribeReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: |-
          ID
          - 为0时按顺序查询字段：name
      name:
        type: string
        description: 角色名称
  GoodsStackHardwareRoleLabelReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      hardwareRoleId:
        type: integer
        format: int64
        description: 关联硬件角色ID,filter[With:HardwareRole]
      id:
        type: integer
        format: int64
        description: ID
      key:
        type: string
        description: 标签键
      value:
        type: string
        description: 标签值
  GoodsStackHardwareRoleReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      fromVersion:
        $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 始于JDStack版本信息,filter[With:FormJdstackRelease]
      hardware:
        type: string
        description: |-
          硬件类型:
          [ server network ]
      id:
        type: integer
        format: int64
        description: ID
      label:
        type: array
        items:
          $ref: '#/definitions/GoodsStackHardwareRoleLabelReply'
        description: 关联硬件角色标签信息,filter[With:HardwareRoleLabel]
      name:
        type: string
        description: 角色名称
      serverPlane:
        type: array
        items:
          $ref: '#/definitions/GoodsStackServerPlaneDescribeReply'
        description: 关联服务器规划信息,filter[With:ServerPlane]
      version:
        type: array
        items:
          $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 关联JDStack版本信息,filter[With:JdstackRelease]
  GoodsStackIacReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      goodsId:
        type: string
        description: 关联标品ID,filter[With:Goods]
      id:
        type: integer
        format: int64
        description: ID
      mode:
        type: string
        description: |-
          交付类型:
          [ deploy upgrade ]
  GoodsStackLess:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      action:
        type: string
        description: |-
          操作行为:
          [ debug online offline upgrade ]
      application:
        $ref: '#/definitions/GoodsStackDescribeApplicationReply'
        description: 应用,filter[With:MetadataApplication]
      category:
        $ref: '#/definitions/GoodsStackDescribeCategoryReply'
        description: 分类,filter[With:MetadataCategory]
      cloudType:
        type: string
        description: |-
          云底座类别:
          [ jdstack cvessel ]
      id:
        type: string
        description: 标品ID
      resource:
        $ref: '#/definitions/GoodsStackDescribeResourceReply'
        description: 服务,filter[With:MetadataResource]
      service:
        $ref: '#/definitions/GoodsStackDescribeServiceReply'
        description: 产品,filter[With:MetadataService]
      version:
        $ref: '#/definitions/GoodsStackVersionDescribeReply'
        description: 版本,filter[With:JdstackRelease]
      virtualGoods:
        type: string
        description: |-
          逻辑属性:
          [ no yes ]
  GoodsStackMetadataCreateReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      application:
        $ref: '#/definitions/GoodsStackCreateApplicationReq'
      category:
        $ref: '#/definitions/GoodsStackCreateCategoryReq'
      resource:
        $ref: '#/definitions/GoodsStackCreateResourceReq'
      service:
        $ref: '#/definitions/GoodsStackCreateServiceReq'
  GoodsStackMiddlewareParams:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      database:
        type: string
        description: 数据库
      password:
        type: string
        description: 密码
      user:
        type: string
        description: 用户名
  GoodsStackMiddlewareReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      coreLimit:
        type: integer
        format: int32
        description: CPU极限/‰核
      coreRequest:
        type: integer
        format: int32
        description: CPU申请/‰核
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      diskLimit:
        type: integer
        format: int32
        description: 硬盘极限/Gi
      diskRequest:
        type: integer
        format: int32
        description: 硬盘申请/Gi
      domainUse:
        type: string
        description: |-
          域名类型:
          [ loadBalancer a ]
      id:
        type: integer
        format: int64
        description: ID
      kind:
        type: string
        description: |-
          软件类型:
          [ mysql postgresql elasticsearch redis zookeeper etcd clickhouse kafka mongodb ]
      memoryLimit:
        type: integer
        format: int32
        description: 内存极限/Mi
      memoryRequest:
        type: integer
        format: int32
        description: 内存申请/Mi
      name:
        type: string
        description: 中间件唯一标识
      params:
        $ref: '#/definitions/GoodsStackMiddlewareParams'
        description: 配置
      port:
        type: integer
        format: int32
        description: 域名端口号
      protocol:
        type: string
        description: |-
          协议:
          [ http https tcp ]
      shareMode:
        type: string
        description: |-
          共享类型:
          [ no yes ]
      targetPort:
        type: integer
        format: int32
        description: 服务端口号
      urlTemplate:
        type: string
        description: URL
  GoodsStackServerPlaneDescribeReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      id:
        type: integer
        format: int64
        description: ID
      name:
        type: string
        description: 规划名称
      plane:
        type: string
        description: |-
          服务器规划:
          [ controlPlane dataPlane managementPlane network networkGroup1 compute storage security basicService sykwingSource iaas ]
      tag:
        type: string
        description: 项目标识
  GoodsStackSyncGoodsActionReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      offlineCount:
        type: integer
        format: int32
        description: 被设置为offline状态的标品数量
  GoodsStackUnifiedMetadataCreateReq:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      content:
        type: string
        description: 元数据内容
      versionId:
        type: string
        description: 元数据版本名称子版本号
      versionName:
        type: string
        description: 商业平台元数据及其他类型元数据版本名称
    required:
      - versionName
      - content
  GoodsStackVersionDescribeReply:
    x-jdcloud-module: jdstackzeus
    type: object
    properties:
      cloudType:
        type: string
        description: |-
          版本类型:
          [ jdstack cvessel ]
      createdAt:
        type: string
        description: 创建时间,UTC格式(毫秒级24位)
      description:
        type: string
        description: 描述
      id:
        type: integer
        format: int32
        description: JDStack版本ID,特殊情况下使用
      locked:
        type: string
        description: |-
          是否锁定:
          [ no yes ]
      nextPlatform:
        type: string
        description: 后序版本,filter[With:Next]
      prevPlatform:
        type: string
        description: 前序版本,filter[With:Prev]
      relatedPlatform:
        type: string
        description: 关联版本,用于描述JDStack关联的云舰版本号
      version:
        type: string
        description: JDStack版本
